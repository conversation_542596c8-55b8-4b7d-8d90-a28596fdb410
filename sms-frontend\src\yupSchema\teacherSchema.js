import * as yup from "yup";

export const teacherSchema = yup.object({
  name: yup
    .string()
    .min(3, "Student name must contain 3 characters.")
    .required("Student name is requried"),
  email: yup
    .string()
    .email("It must be an Email.")
    .required("Email is requried"),
  qualification: yup.string().required("Qualification is requried"),
  age: yup.string().required("Age is a required field."),
  gender: yup.string().required("Gender is a required field."),

  password: yup
    .string()
    .min(8, "Password must contain 8 characters.")
    .required("Password is a requried field"),
  confirm_password: yup
    .string()
    .oneOf([yup.ref("password")], "Confirm Password Must Match With Password.")
    .required("Confirm password is a requried field"),
});

export const teacherEditSchema = yup.object({
  name: yup
    .string()
    .min(3, "Student name must contain 3 characters.")
    .required("Student name is requried"),
  email: yup
    .string()
    .email("It must be an Email.")
    .required("Email is requried"),
  qualification: yup.string().required("Qualification is requried"),
  age: yup.string().required("Age is a required field."),
  gender: yup.string().required("Gender is a required field."),

  password: yup.string().min(8, "Password must contain 8 characters."),

  confirm_password: yup
    .string()
    .oneOf([yup.ref("password")], "Confirm Password Must Match With Password."),
});
