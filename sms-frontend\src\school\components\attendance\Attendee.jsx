import { Box, FormControl, InputLabel, MenuItem, Select, Typography } from "@mui/material";
import { useEffect, useState } from "react"
import Teacher from "../../../teacher/Teacher";
import Teachers from "../teachers/Teachers";
import axios from "axios";
import { server } from "../../../server";
import { Message } from "@mui/icons-material";

export default function Attendee({classId , handleMessage ,message}){
 const [teachers , setTeachers]=useState([])
 const [selectedTeacher , setselectedTeacher]=useState("")


 const handleSubmit = async () => {
  
    try {
      if (selectedTeacher){
        const response = await axios.patch(`${server}/api/class/update/${classId}`, {attendee:selectedTeacher});
        console.log(response, "Submit attendee");
        handleMessage("Success in attendee save/update", 'success')
      }
      else{
           alert ("Please Select Teacher first .")
      }
    } catch (error) {
      console.log("Error:", error);
    }
  };

  const [attendee, setAttendee] = useState(null)
  const fetchClassDetails = async () => {
    if (classId) {
      try {
        const response = await axios.get(`${server}/api/class/single/${classId}`);
        setAttendee(response.data.data.attendee?response.data.data.attendee:null)
        console.log("SINGLE CLASS", response);
      } catch (error) {
        console.log("ERROR", error);
      }
    }
  };




     const fetchTeachers = () => {
        
        axios
          .get(`${server}/api/teacher/fetch-with-query`, )
          .then((response) => {
            setTeachers(response.data.teachers);
          })
          .catch((e) => {
            console.log("Error in fetching Teachers.");
          });
      };
 useEffect(()=>{
  console.log("CLASS ID", classId)
  fetchClassDetails();
  fetchTeachers();
 },[classId , message])

    return(<>
        <h1>Attendee</h1>
        <Box>
        {attendee && <Box sx={{display:"flex", flexDirection:"row" , justifyContent:"center"}} component={'div'}>
            <Typography variant="h5"> Attendee Teacher : </Typography>
            <Typography variant="h5">{attendee.name}</Typography>
        </Box>}
        <Box>
        <FormControl sx={{ width: "230px", marginLeft: "10px" }}>
          <InputLabel id="demo-simple-select-label">Select Teacher</InputLabel>
          <Select
            label="Select Teacher"
            value={selectedTeacher}
            onChange={(e) => {
              setselectedTeacher(e.target.value)
            }}
          >
            <MenuItem value=""> Un-Select</MenuItem>
            {teachers &&
              teachers.map((x) => {
                return (
                  <MenuItem key={x._id} value={x._id}>
                    {x.name}
                  </MenuItem>
                );
              })}
          </Select>
         </FormControl>
         <button onClick={handleSubmit}>{ attendee?"Change Attendee":"Select Attendee"}</button>
        </Box>
        </Box>
        </>)
        
       

}