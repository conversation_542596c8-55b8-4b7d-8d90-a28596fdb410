import { Box, Button, Paper, TextField, Typography } from "@mui/material";
import { useFormik } from "formik";
import { classSchema } from "../../../yupSchema/classSchema";
import { server } from "../../../server";
import axios from "axios";
import { useEffect, useState } from "react";

// material ui icon
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import MessageSnackbar from "../../../basic utility component/snakebar/MessageSnakebar";

export default function Class() {
  const [message, setMessage] = useState("");
  const [messageType, setMessageType] = useState("success");
  const handleMessageClose = () => {
    setMessage("");
  };
  const [classes, setClasses] = useState([]);
  const [edit, setEdit] = useState(false);
  const [editId, setEditId] = useState(null);

  const handleEdit = (id, class_text, class_num) => {
    console.log(id);
    setEdit(true);
    setEditId(id);
    Formik.setFieldValue("class_text", class_text);
    Formik.setFieldValue("class_num", class_num);
  };
  const cancelEdit = () => {
    setEdit(false);
    setEditId(null);
    Formik.setFieldValue("class_text", "");
    Formik.setFieldValue("class_num", "");
  };

  const handleDelete = (id) => {
    console.log(id);
    axios
      .delete(`${server}/api/class/delete/${id}`)
      .then((response) => {
        setMessage(response.data.message);
        setMessageType("success");
      })
      .catch((e) => {
        console.log(e);
        setMessage("Error in delete.");
        setMessageType("error");
      });
  };
  const Formik = useFormik({
    initialValues: {
      class_text: "",
      class_num: "",
    },
    validationSchema: classSchema,
    onSubmit: (values) => {
      console.log(values);
      if (edit) {
        axios
          .patch(`${server}/api/class/update/${editId}`, { ...values })
          .then((response) => {
            cancelEdit();
            setMessage(response.data.message);
            setMessageType("Success");
          })
          .catch((e) => {
            console.log("Error in updating class", e);
            setMessage("error in updating Class");
            setMessageType("error");
          });
        Formik.resetForm();
      } else {
        axios
          .post(`${server}/api/class/create`, { ...values })
          .then((response) => {
            setMessage(response.data.message);
            setMessageType("Success");
          })
          .catch((e) => {
            console.log("Error in Adding class", e);
            setMessage("error in saving Class");
            setMessageType("error");
          });
        Formik.resetForm();
      }
    },
  });
  const fetchAllClasses = () => {
    axios
      .get(`${server}/api/class/all`)
      .then((response) => {
        setClasses(response.data.data);
      })
      .catch((e) => {
        console.log("Error in fetching all classes", e);
      });
  };

  useEffect(() => {
    fetchAllClasses();
  }, [message]);
  return (
    <>
      {message && (
        <MessageSnackbar
          message={message}
          type={messageType}
          handleClose={handleMessageClose}
        />
      )}
      <h1>Class</h1>
      <Box
        component="form"
        sx={{
          "& > :not(style)": { m: 1 },
          display: "flex",
          flexDirection: "column",
          width: "60vw",
          minWidth: "230px",
          margin: "auto",
        }}
        noValidate
        autoComplete="off"
        onSubmit={Formik.handleSubmit}
      >
        {edit ? (
          <Typography
            variant="h4"
            sx={{ textAlign: "center", fontWeight: 700 }}
          >
            Edit Class
          </Typography>
        ) : (
          <Typography
            variant="h4"
            sx={{ textAlign: "center", fontWeight: 700 }}
          >
            Add Classes
          </Typography>
        )}
        <TextField
          name="class_text"
          label="Class Name"
          value={Formik.values.class_text}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.class_text && Formik.errors.class_text && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.class_text}
          </p>
        )}

        <TextField
          name="class_num"
          label="Class Number"
          value={Formik.values.class_num}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.class_num && Formik.errors.class_num && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.class_num}
          </p>
        )}
        <Button type="submit" variant="contained">
          Save
        </Button>

        {edit && (
          <Button
            onClick={() => {
              cancelEdit();
            }}
            type="submit"
            variant="contained"
          >
            Cancel
          </Button>
        )}
      </Box>
      <Box
        component={"div"}
        sx={{ display: "flex", flexDirection: "row", flexWrap: "wrap" }}
      >
        {classes &&
          classes.map((x) => {
            return (
              <Paper key={x._id} sx={{ m: 2, p: 2 }}>
                <Box component={"div"}>
                  <Typography>
                    Class: {x.class_text} [{x.class_num}]
                  </Typography>
                </Box>
                <Box component={"div"}>
                  <Button
                    onClick={() => {
                      handleEdit(x._id, x.class_text, x.class_num);
                    }}
                  >
                    <EditIcon />
                  </Button>
                  <Button
                    onClick={() => {
                      handleDelete(x._id);
                    }}
                    sx={{ color: "red" }}
                  >
                    <DeleteIcon />
                  </Button>
                </Box>
              </Paper>
            );
          })}
      </Box>
    </>
  );
}
