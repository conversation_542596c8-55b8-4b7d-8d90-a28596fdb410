import React, { useState, useRef } from "react";
import { QrReader } from "react-qr-reader";
import axios from "axios";
import { server } from "../../../server";
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Container,
  CssBaseline,
  CardMedia,
} from "@mui/material";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import MessageSnackbar from "../../../basic utility component/snakebar/MessageSnakebar";
import QrCodeScannerIcon from "@mui/icons-material/QrCodeScanner";

const theme = createTheme({
  typography: {
    h5: {
      fontWeight: 600,
    },
    body1: {
      marginBottom: "8px",
    },
  },
});

export default function QrScanner() {
  const [qrResult, setQrResult] = useState(null);
  const [data, setData] = useState(null);
  const [message, setMessage] = useState("");
  const [messageType, setMessageType] = useState("");
  const lastScannedRef = useRef("");
  const cooldownRef = useRef(false);

  const handleMessageClose = () => {
    setMessage("");
  };

  const handleResult = async (result) => {
    try {
      const scannedText = result?.text;

      if (
        scannedText &&
        scannedText !== lastScannedRef.current &&
        !cooldownRef.current
      ) {
        lastScannedRef.current = scannedText;
        cooldownRef.current = true;
        setQrResult(scannedText);

        const beep = new Audio("/beep.mp3");
        beep.play();

        try {
          const res = await axios.post(
            `${server}/api/Qr-code-attendance/mark`,
            { qr_id: scannedText }
          );

          setData(res.data.attendanceData);
          console.log(data);
          setMessage(res.data.message);
          setMessageType("success");
        } catch (err) {
          console.error("Axios Error:", err);
          setMessage("Error marking attendance");
          setMessageType("error");
        }

        setTimeout(() => {
          cooldownRef.current = false;
          lastScannedRef.current = "";
          setQrResult(null);
          setMessage("");
        }, 3000);
      }
    } catch (error) {
      console.error("handleResult error:", error);
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Container
        maxWidth="sm"
        sx={{
          mt: 4,
          display: "flex",

          alignItems: "center",
          justifyContent: "center",
          gap: 3,
        }}
      >
        {message && (
          <MessageSnackbar
            message={message}
            type={messageType}
            handleClose={handleMessageClose}
          />
        )}

        <Box
          sx={{
            mb: 4,
            mx: "auto",
            maxWidth: "100%",
            borderRadius: 2,
            overflow: "hidden",
            boxShadow: 3,
          }}
        >
          <Typography variant="h4" align="center" gutterBottom>
            Scan QR Code
          </Typography>
          <QrReader
            constraints={{ facingMode: "environment" }}
            onResult={handleResult}
            containerStyle={{ width: "0%" }}
          />
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: 250,
              backgroundColor: "#f5f5f5",
              borderRadius: 2,
              overflow: "hidden",
            }}
          >
            <img
              src="/qr-code.gif"
              alt="Scanning..."
              style={{ maxWidth: "100%", maxHeight: "100%" }}
            />
          </Box>
        </Box>
        {data && (
          <Card sx={{ boxShadow: 4 }}>
            <CardContent>
              <Typography variant="h5" align="center" sx={{ mb: 3 }}>
                User Profile
              </Typography>
              <CardMedia
                component="img"
                height="auto"
                image={`/images/uploaded/student/${data?.student.student_image}`}
                alt="green iguana"
              />
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body1">
                    <strong>Name</strong>
                  </Typography>
                  <Typography variant="body1">
                    <strong>Email</strong>
                  </Typography>
                  <Typography variant="body1">
                    <strong>Age</strong>
                  </Typography>
                  <Typography variant="body1">
                    <strong>Gender</strong>
                  </Typography>
                  <Typography variant="body1">
                    <strong>Class</strong>
                  </Typography>
                  <Typography variant="body1">
                    <strong>Status</strong>
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body1">{data?.student.name}</Typography>
                  <Typography variant="body1">{data?.student.email}</Typography>
                  <Typography variant="body1">{data?.student.age}</Typography>
                  <Typography variant="body1">
                    {data?.student.gender}
                  </Typography>
                  <Typography variant="body1">
                    {data?.class.class_text}/{data?.class.class_num}
                  </Typography>
                  <Typography variant="body1">{data?.status}</Typography>
                </Grid>
              </Grid>

              <Box sx={{ textAlign: "center", mt: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: "bold" }}>
                  {data?.school.school_name}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        )}
      </Container>
    </ThemeProvider>
  );
}
