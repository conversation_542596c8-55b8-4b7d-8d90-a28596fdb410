// import * as yup from "yup";

// export const periodSchema = yup.object({
//   teacher: yup.string().required("Teacher is required"),
//   subject: yup.string().required("Subject is required"),
//   class: yup.string().required("class is required"),
//   period: yup.string().required("Period is required"),
//   start_time: yup.date().required("Start Time  is is required"),
//   end_time: yup.date().required("End Time  is is required"),
// });
import * as yup from "yup";
import moment from "moment";

// Custom time format validation
const timeFormat = "h:mm A";

export const periodSchema = yup.object({
  teacher: yup.string().required("Teacher is required"),
  subject: yup.string().required("Subject is required"),
  class: yup.string().required("Class is required"),
  period: yup.string().required("Period is required"),
  days: yup.string().required("Period is required"),
  start_time: yup
    .string()
    .required("Start Time is required")
    .test(
      "is-valid-time",
      "Start Time must be in h:mm e.g(9:00 A.M or 9:30 P.M ) A format",
      (value) => moment(value, timeFormat, true).isValid()
    ),
  end_time: yup
    .string()
    .required("End Time is required")
    .test(
      "is-valid-time",
      "End Time must be in h:mm e.g(9:00 A.M or 9:30 P.M ) A format",
      (value) => moment(value, timeFormat, true).isValid()
    ),
});
