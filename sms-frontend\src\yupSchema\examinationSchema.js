import * as yup from "yup";

export const examinationSchema = yup.object().shape({
  date: yup.date().required("Date is required."),
  subject: yup.string().required("Subject  require."),
  Class: yup.string().required("Class  require."),
  teacher: yup.string().required("Teacher require."),
  invigilator: yup.string().required("Invigilator  is requrie."),
  examType: yup.string().required("Exam type is required."),
  startTime: yup.string().required("Start date is required"),
  endTime: yup.string().required("End date is required"),
});
