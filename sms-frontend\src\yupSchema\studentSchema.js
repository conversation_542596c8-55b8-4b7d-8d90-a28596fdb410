import * as yup from "yup";

export const studentSchema = yup.object({
  name: yup
    .string()
    .min(3, "Student name must contain 3 characters.")
    .required("Student name is requried"),
  email: yup
    .string()
    .email("It must be an Email.")
    .required("Email is requried"),
  student_class: yup.string().required("Student Class is a required field."),
  age: yup.string().required("Age is a required field."),
  gender: yup.string().required("Gender is a required field."),
  guardian: yup
    .string()
    .min(4, "Must contain at least 4 characters.")
    .required("Guardian is a required field."),
  guardian_phone: yup
    .string()
    .matches(/^[0-9]+$/, "Must be a valid phone number")
    .min(11, "Must contain at least 11 digits")
    .max(11, "Cannot exceed 11 digits")
    .required("Guardian phone is a required field."),
  password: yup
    .string()
    .min(8, "Password must contain 8 characters.")
    .required("Password is a requried field"),
  confirm_password: yup
    .string()
    .oneOf([yup.ref("password")], "Confirm Password Must Match With Password.")
    .required("Confirm password is a requried field"),
});

export const studentEditSchema = yup.object({
  name: yup
    .string()
    .min(3, "Student name must contain 3 characters.")
    .required("Student name is requried"),
  email: yup
    .string()
    .email("It must be an Email.")
    .required("Email is requried"),
  student_class: yup.string().required("Student Class is a required field."),
  age: yup.string().required("Age is a required field."),
  gender: yup.string().required("Gender is a required field."),
  guardian: yup
    .string()
    .min(4, "Must contain at least 4 characters.")
    .required("Guardian is a required field."),
  guardian_phone: yup
    .string()
    .matches(/^[0-9]+$/, "Must be a valid phone number")
    .min(11, "Must contain at least 11 digits")
    .max(11, "Cannot exceed 11 digits")
    .required("Guardian phone is a required field."),
  password: yup.string().min(8, "Password must contain 8 characters."),

  confirm_password: yup
    .string()
    .oneOf([yup.ref("password")], "Confirm Password Must Match With Password."),
});
