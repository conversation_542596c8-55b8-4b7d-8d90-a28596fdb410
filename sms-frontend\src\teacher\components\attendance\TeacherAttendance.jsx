import axios from "axios";
import { useEffect, useState } from "react";
import { server } from "../../../server";
import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import Alert from "@mui/material/Alert";
import CheckIcon from "@mui/icons-material/Check";
import MessageSnackbar from "../../../basic utility component/snakebar/MessageSnakebar";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import dayjs from "dayjs";
import moment from "moment";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
export default function AttendanceTeacher() {
  const [classes, setClasses] = useState([]);
  const [selectedClass, setSelectedClass] = useState("");
  const [date, setDate] = useState(dayjs());
  const [attendanceStatus, setAttendanceStatus] = useState({});
  const [students, setStudents] = useState([]);
  const [message, setMessage] = useState("");
  const [attendanceChecked, setAttendanceChecked] = useState(false);
  const [messageType, setMessageType] = useState("success");
  const handleMessageClose = () => {
    setMessage("");
  };

  const handleAttendance = (studentId, status) =>
    setAttendanceStatus((prevStatus) => ({
      ...prevStatus,
      [studentId]: status,
    }));

  const singleStudentAttendance = async (studentId, status) => {
    try {
      // studentId, date, status, classId
      const response = await axios.post(`${server}/api/attendance/mark`, {
        studentId,
        date: date,
        classId: selectedClass,
        status,
      });

      console.log("marking attendance", response);
    } catch (error) {
      console.log("Error-> marking Attendee:", error);
    }
  };

  const submitAttendance = async () => {
    try {
      await Promise.all(
        students.map((student) =>
          singleStudentAttendance(student._id, attendanceStatus[student._id])
        )
      );
      setMessage("Attendance Submitted Successfully");
      setMessageType("success");
      setAttendanceChecked(true);
      setStudents([]);
    } catch (error) {
      setMessage("Error in Submitting Attendance");
      setMessageType("error");
      console.log("Error submitting attendance:", error);
    }
  };

  const fetchAttendeeClass = async () => {
    try {
      const response = await axios.get(`${server}/api/class/attendee`);
      console.log("attendee class", response);
      setClasses(response.data.data);
      if (response.data.data.length > 0) {
        setSelectedClass(response.data.data[0]._id);
      }
    } catch (error) {
      console.log("error fetching Attendee class.", error);
    }
  };

  const checkAttendanceAndFetchStudents = async () => {
    try {
      if (selectedClass && date) {
        const responseStudent = await axios.get(
          `${server}/api/student/fetch-with-query`,
          {
            params: { student_class: selectedClass },
          }
        );

        const responseCheck = await axios.get(
          `${server}/api/attendance/check/${selectedClass}/${date}`
        );
        console.log("Check", responseCheck);
        if (!responseCheck.data.attendanceTaken) {
          setStudents(responseStudent.data.students);

          responseStudent.data.students.forEach((student) => {
            handleAttendance(student._id, "present");
          });
        } else {
          setAttendanceChecked(true);
        }
      }
    } catch (error) {
      console.log("Error in check attendance", error);
    }
  };

  // const fetchStudents = () => {
  //   axios
  //     .get(`${server}/api/student/fetch-with-query`, {
  //       params: { student_class: selectedClass },
  //     })
  //     .then((response) => {
  //       console.log(response.data.students);
  //       setStudents(response.data.students);
  //       response.data.students.forEach((student) => {
  //         handleAttendance(student._id, "present");
  //       });
  //     })
  //     .catch((e) => {
  //       console.log("Error in fetching Students.");
  //     });
  // };

  useEffect(() => {
    fetchAttendeeClass();
  }, []);

  useEffect(() => {
    checkAttendanceAndFetchStudents();
  }, [selectedClass, date]);

  return (
    <>
      {message && (
        <MessageSnackbar
          message={message}
          type={messageType}
          handleClose={handleMessageClose}
        />
      )}
      <h1>Attendance Teacher</h1>
      {classes.length > 0 ? (
        <Box
          sx={{
            display: "flex",
            marginBottom: "10px",
            justifyContent: "space-between",
          }}
        >
          <Alert icon={<CheckIcon fontSize="inherit" />} severity="success">
            You are attende of {classes.length} class
          </Alert>
          {/* class */}
          <FormControl sx={{ marginTop: "20px", width: "20vw" }}>
            <InputLabel id="demo-simple-select-label">Class</InputLabel>
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              label="Class"
              name="class"
              onChange={(e) => {
                setSelectedClass(e.target.value);
                setAttendanceChecked(false);
              }}
              value={selectedClass}
            >
              <MenuItem value={""}>Un Select</MenuItem>
              {classes &&
                classes.map((x) => {
                  return (
                    <MenuItem key={x._id} value={x._id}>
                      {x.class_text} ({x.class_num})
                    </MenuItem>
                  );
                })}
            </Select>
          </FormControl>
          {/* date */}
          <LocalizationProvider dateAdapter={AdapterDayjs} fullWidth>
            <DemoContainer components={["DatePicker"]} fullWidth>
              <DatePicker
                fullWidth
                label="date"
                value={date}
                onChange={(newDate) => {
                  setDate(newDate);
                }}
              />
            </DemoContainer>
          </LocalizationProvider>
        </Box>
      ) : (
        <Alert icon={<CheckIcon fontSize="inherit" />} severity="error">
          You are not attende of any class
        </Alert>
      )}

      {students.length > 0 ? (
        <TableContainer component={Paper}>
          <Table sx={{ minWidth: 650 }} aria-label="simple table">
            <TableHead>
              <TableRow>
                <TableCell>Student Name</TableCell>

                <TableCell>Action</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {students.map((student) => (
                <TableRow
                  key={student._id}
                  sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
                >
                  <TableCell>{student.name}</TableCell>
                  <TableCell>
                    <FormControl sx={{ marginTop: "20px", width: "20vw" }}>
                      <InputLabel id="demo-simple-select-label">
                        Attendance
                      </InputLabel>
                      <Select
                        labelId="demo-simple-select-label"
                        id="demo-simple-select"
                        label="Attendance"
                        name="attendance"
                        onChange={(e) => {
                          handleAttendance(student._id, e.target.value);
                        }}
                        value={attendanceStatus[student._id]}
                      >
                        <MenuItem value={"present"}>Present</MenuItem>
                        <MenuItem value={"absent"}>Absent</MenuItem>
                      </Select>
                    </FormControl>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          <Button variant="contained" onClick={submitAttendance}>
            Take Attendance
          </Button>
        </TableContainer>
      ) : (
        <Alert icon={<CheckIcon fontSize="inherit" />} severity="error">
          {attendanceChecked
            ? "Attendance  taken for this class"
            : "There are no students "}
        </Alert>
      )}
    </>
  );
}
