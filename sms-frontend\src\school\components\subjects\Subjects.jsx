import { Box, Button, Paper, TextField, Typography, Grid } from "@mui/material";
import { useFormik } from "formik";
import { subjectSchema } from "../../../yupSchema/subjectSchema";
import { server } from "../../../server";
import axios from "axios";
import { useEffect, useState } from "react";

// material ui icon
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import MessageSnackbar from "../../../basic utility component/snakebar/MessageSnakebar";

export default function Subjects() {
  const [message, setMessage] = useState("");
  const [messageType, setMessageType] = useState("success");
  const handleMessageClose = () => {
    setMessage("");
  };
  const [subjects, setSubjects] = useState([]);
  const [edit, setEdit] = useState(false);
  const [editId, setEditId] = useState(null);

  const handleEdit = (id, subject_name, subject_codename) => {
    console.log(id);
    setEdit(true);
    setEditId(id);
    Formik.setFieldValue("subject_name", subject_name);
    Formik.setFieldValue("subject_codename", subject_codename);
  };
  const cancelEdit = () => {
    setEdit(false);
    setEditId(null);
    Formik.setFieldValue("subject_name", "");
    Formik.setFieldValue("subject_codename", "");
  };

  const handleDelete = (id) => {
    console.log(id);
    axios
      .delete(`${server}/api/subject/delete/${id}`)
      .then((response) => {
        setMessage(response.data.message);
        setMessageType("success");
      })
      .catch((e) => {
        console.log(e);
        setMessage("Error in delete.");
        setMessageType("error");
      });
  };
  const Formik = useFormik({
    initialValues: {
      subject_name: "",
      subject_codename: "",
    },
    validationSchema: subjectSchema,
    onSubmit: (values) => {
      console.log(values);
      if (edit) {
        axios
          .patch(`${server}/api/subject/update/${editId}`, { ...values })
          .then((response) => {
            cancelEdit();
            setMessage(response.data.message);
            setMessageType("Success");
          })
          .catch((e) => {
            console.log("Error in updating subject", e);
            setMessage("error in updating Subject");
            setMessageType("error");
          });
        Formik.resetForm();
      } else {
        axios
          .post(`${server}/api/subject/create`, { ...values })
          .then((response) => {
            setMessage(response.data.message);
            setMessageType("Success");
          })
          .catch((e) => {
            console.log("Error in Adding subject", e);
            setMessage("error in saving Subject");
            setMessageType("error");
          });
        Formik.resetForm();
      }
    },
  });
  const fetchAllSubjects = () => {
    axios
      .get(`${server}/api/subject/all`)
      .then((response) => {
        setSubjects(response.data.data);
      })
      .catch((e) => {
        console.log("Error in fetching all subjects", e);
      });
  };

  useEffect(() => {
    fetchAllSubjects();
  }, [message]);
  return (
    <>
      {message && (
        <MessageSnackbar
          message={message}
          type={messageType}
          handleClose={handleMessageClose}
        />
      )}
      <h1>Subject</h1>
      <Box
        component="form"
        sx={{
          "& > :not(style)": { m: 1 },
          display: "flex",
          flexDirection: "column",
          width: "60vw",
          minWidth: "230px",
          margin: "auto",
        }}
        noValidate
        autoComplete="off"
        onSubmit={Formik.handleSubmit}
      >
        {edit ? (
          <Typography
            variant="h4"
            sx={{ textAlign: "center", fontWeight: 700 }}
          >
            Edit Subject
          </Typography>
        ) : (
          <Typography
            variant="h4"
            sx={{ textAlign: "center", fontWeight: 700 }}
          >
            Add Subjects
          </Typography>
        )}
        <TextField
          name="subject_name"
          label="Subject Name"
          value={Formik.values.subject_name}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.subject_name && Formik.errors.subject_name && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.subject_name}
          </p>
        )}

        <TextField
          name="subject_codename"
          label="Subject Code"
          value={Formik.values.subject_codename}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.subject_codename && Formik.errors.subject_codename && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.subject_codename}
          </p>
        )}
        <Button type="submit" variant="contained">
          Save
        </Button>

        {edit && (
          <Button
            onClick={() => {
              cancelEdit();
            }}
            type="submit"
            variant="contained"
          >
            Cancel
          </Button>
        )}
      </Box>
      <Box
        component={"div"}
        sx={{ display: "flex", flexDirection: "row", flexWrap: "wrap" }}
      >
        {subjects &&
          subjects.map((x) => {
            return (
              <Paper key={x._id} sx={{ m: 2, p: 2 }}>
                <Box component={"div"}>
                  <Typography>
                    Subject: {x.subject_name} [{x.subject_codename}]
                  </Typography>
                </Box>
                <Box component={"div"}>
                  <Button
                    onClick={() => {
                      handleEdit(x._id, x.subject_name, x.subject_codename);
                    }}
                  >
                    <EditIcon />
                  </Button>
                  <Button
                    onClick={() => {
                      handleDelete(x._id);
                    }}
                    sx={{ color: "red" }}
                  >
                    <DeleteIcon />
                  </Button>
                </Box>
              </Paper>
            );
          })}
      </Box>
    </>
  );
}
