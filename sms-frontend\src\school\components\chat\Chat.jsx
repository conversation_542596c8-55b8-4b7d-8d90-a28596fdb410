import { useState, useEffect, useContext, useRef } from "react";
import { ChatContext } from "../../../context/ChatContext";
import { AuthContext } from "../../../context/AuthContext";
import axios from "axios";
import { server } from "../../../server";
import MessageSnackbar from "../../../basic utility component/snakebar/MessageSnakebar";
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  TextField,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  Badge,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import AddIcon from "@mui/icons-material/Add";
import SendIcon from "@mui/icons-material/Send";
import GroupIcon from "@mui/icons-material/Group";
import PersonIcon from "@mui/icons-material/Person";
import AdminPanelSettingsIcon from "@mui/icons-material/AdminPanelSettings";
import PersonRemoveIcon from "@mui/icons-material/PersonRemove";

const Chat = () => {
  const { user } = useContext(AuthContext);
  const {
    chatGroups,
    messages,
    currentGroup,
    unreadCounts,
    fetchChatGroups,
    joinChatRoom,
    sendMessage,
    createChatGroup,
    updateChatGroup,
    deleteChatGroup,
    loadMoreMessages,
    loadingMore,
    hasMore,
  } = useContext(ChatContext);

  const [newMessage, setNewMessage] = useState("");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showManageMembersModal, setShowManageMembersModal] = useState(false);
  const [newGroupData, setNewGroupData] = useState({
    name: "",
    description: "",
    members: [],
  });
  const [teachers, setTeachers] = useState([]);
  const [students, setStudents] = useState([]);
  const [filteredStudents, setFilteredStudents] = useState([]);
  const [selectedMembers, setSelectedMembers] = useState([]);
  const [selectedAdmins, setSelectedAdmins] = useState([]);
  const [messageAlert, setMessageAlert] = useState({
    open: false,
    message: "",
    type: "success",
  });
  const [currentGroupDetails, setCurrentGroupDetails] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState({
    open: false,
    groupId: null,
  });
  const [classes, setClasses] = useState([]);
  const [selectedClass, setSelectedClass] = useState("");
  const [localLoadingMore, setLocalLoadingMore] = useState(false);
  const [prevMessagesLength, setPrevMessagesLength] = useState(0);

  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);

  useEffect(() => {
    fetchChatGroups();
    fetchTeachersAndStudents();
    fetchClasses();
  }, []);

  useEffect(() => {
    if (currentGroup) {
      const groupDetails = chatGroups.find((g) => g._id === currentGroup);
      setCurrentGroupDetails(groupDetails);
    } else {
      setCurrentGroupDetails(null);
    }
  }, [currentGroup, chatGroups]);

  const fetchClasses = async () => {
    try {
      const response = await axios.get(`${server}/api/class/all`);
      setClasses(response.data.data || []);
    } catch (error) {
      console.error("Error fetching classes:", error);
    }
  };

  const fetchTeachersAndStudents = async () => {
    try {
      const teachersRes = await axios.get(
        `${server}/api/teacher/fetch-with-query`
      );
      const studentsRes = await axios.get(
        `${server}/api/student/fetch-with-query`
      );

      setTeachers(teachersRes.data.teachers || []);
      setStudents(studentsRes.data.students || []);
      setFilteredStudents(studentsRes.data.students || []);
    } catch (error) {
      console.error("Error fetching users:", error);
      setMessageAlert({
        open: true,
        message: "Failed to fetch users",
        type: "error",
      });
    }
  };

  const handleClassChange = async (e) => {
    const classId = e.target.value;
    setSelectedClass(classId);

    if (!classId) {
      // If no class is selected, show all students
      setFilteredStudents(students);
    } else {
      // Filter students by class
      try {
        const response = await axios.get(
          `${server}/api/student/fetch-with-query`,
          {
            params: { student_class: classId },
          }
        );
        setFilteredStudents(response.data.students || []);
      } catch (error) {
        console.error("Error fetching students by class:", error);
        setMessageAlert({
          open: true,
          message: "Failed to fetch students by class",
          type: "error",
        });
      }
    }
  };

  const handleCreateGroup = async () => {
    try {
      const membersData = [
        ...selectedMembers.map((m) => ({
          user: m.id,
          userType: m.type,
          isAdmin: selectedAdmins.some(
            (a) => a.id === m.id && a.type === m.type
          ),
        })),
      ];

      const response = await createChatGroup({
        name: newGroupData.name,
        description: newGroupData.description,
        members: membersData,
      });

      setShowCreateModal(false);
      setNewGroupData({ name: "", description: "" });
      setSelectedMembers([]);
      setSelectedAdmins([]);
      setSelectedClass(""); // Reset class filter
      setFilteredStudents(students); // Reset to all students

      setMessageAlert({
        open: true,
        message: "Chat group created successfully",
        type: "success",
      });
    } catch (error) {
      console.error("Error creating group:", error);
      setMessageAlert({
        open: true,
        message: "Failed to create chat group",
        type: "error",
      });
    }
  };

  const handleSendMessage = (e) => {
    e.preventDefault();
    if (newMessage.trim()) {
      sendMessage(newMessage);
      setNewMessage("");

      // Focus back on the input field after sending
      setTimeout(() => {
        const inputField = document.getElementById("chat-message-input");
        if (inputField) inputField.focus();
      }, 0);
    }
  };

  const handleMemberSelection = (id, type) => {
    const existingIndex = selectedMembers.findIndex(
      (m) => m.id === id && m.type === type
    );

    if (existingIndex >= 0) {
      // Remove if already selected
      const newSelectedMembers = [...selectedMembers];
      newSelectedMembers.splice(existingIndex, 1);
      setSelectedMembers(newSelectedMembers);

      // Also remove from admins if present
      setSelectedAdmins((prev) =>
        prev.filter((a) => !(a.id === id && a.type === type))
      );
    } else {
      // Add if not selected
      setSelectedMembers((prev) => [...prev, { id, type }]);
    }
  };

  const handleAdminSelection = (id, type) => {
    const existingIndex = selectedAdmins.findIndex(
      (a) => a.id === id && a.type === type
    );

    if (existingIndex >= 0) {
      // Remove if already selected
      const newSelectedAdmins = [...selectedAdmins];
      newSelectedAdmins.splice(existingIndex, 1);
      setSelectedAdmins(newSelectedAdmins);
    } else {
      // Add if not selected
      setSelectedAdmins((prev) => [...prev, { id, type }]);
    }
  };

  const handleDeleteGroup = async () => {
    try {
      if (!confirmDelete.groupId) return;

      await deleteChatGroup(confirmDelete.groupId);

      setConfirmDelete({ open: false, groupId: null });
      setMessageAlert({
        open: true,
        message: "Chat group deleted successfully",
        type: "success",
      });
    } catch (error) {
      console.error("Error deleting group:", error);
      setMessageAlert({
        open: true,
        message: "Failed to delete chat group",
        type: "error",
      });
    }
  };

  const openManageMembersModal = () => {
    if (!currentGroupDetails) return;

    // Initialize selected members from current group
    const members = currentGroupDetails.members.map((m) => ({
      id: m.user,
      type: m.userType,
    }));

    const admins = currentGroupDetails.members
      .filter((m) => m.isAdmin)
      .map((m) => ({
        id: m.user,
        type: m.userType,
      }));

    setSelectedMembers(members);
    setSelectedAdmins(admins);
    setShowManageMembersModal(true);
  };

  const handleUpdateMembers = async () => {
    try {
      if (!currentGroup) return;

      // Get current members
      const currentMembers = currentGroupDetails.members;

      // Members to add (in selected but not in current)
      const membersToAdd = selectedMembers.filter(
        (sm) =>
          !currentMembers.some(
            (cm) => cm.user.toString() === sm.id && cm.userType === sm.type
          )
      );

      // Members to remove (in current but not in selected)
      const membersToRemove = currentMembers.filter(
        (cm) =>
          !selectedMembers.some(
            (sm) => cm.user.toString() === sm.id && cm.userType === sm.type
          )
      );

      // Members whose admin status changed
      const membersToUpdateAdmin = currentMembers.filter((cm) => {
        const isSelected = selectedMembers.some(
          (sm) => cm.user.toString() === sm.id && cm.userType === sm.type
        );

        if (!isSelected) return false;

        const wasAdmin = cm.isAdmin;
        const isNowAdmin = selectedAdmins.some(
          (sa) => cm.user.toString() === sa.id && cm.userType === sa.type
        );

        return wasAdmin !== isNowAdmin;
      });

      // Process all changes
      // Add new members
      for (const member of membersToAdd) {
        await updateChatGroup(currentGroup, {
          action: "add",
          userId: member.id,
          userType: member.type,
          isAdmin: selectedAdmins.some(
            (a) => a.id === member.id && a.type === member.type
          ),
        });
      }

      // Remove members
      for (const member of membersToRemove) {
        await updateChatGroup(currentGroup, {
          action: "remove",
          userId: member.user.toString(),
          userType: member.userType,
        });
      }

      // Update admin status
      for (const member of membersToUpdateAdmin) {
        const isNowAdmin = selectedAdmins.some(
          (sa) =>
            member.user.toString() === sa.id && member.userType === sa.type
        );

        await updateChatGroup(currentGroup, {
          action: "updateAdmin",
          userId: member.user.toString(),
          userType: member.userType,
          isAdmin: isNowAdmin,
        });
      }

      setShowManageMembersModal(false);
      setSelectedMembers([]);
      setSelectedAdmins([]);
      setSelectedClass(""); // Reset class filter
      setFilteredStudents(students); // Reset to all students

      setMessageAlert({
        open: true,
        message: "Group members updated successfully",
        type: "success",
      });
    } catch (error) {
      console.error("Error updating members:", error);
      setMessageAlert({
        open: true,
        message: "Failed to update group members",
        type: "error",
      });
    }
  };

  // Add this function to handle selecting all students
  const handleSelectAllStudents = () => {
    // Get all student IDs from the filtered list
    const studentIds = filteredStudents.map((student) => ({
      id: student._id,
      type: "Student",
    }));

    // For each student, check if they're already in the selectedMembers array
    const newSelectedMembers = [...selectedMembers];

    studentIds.forEach((student) => {
      const alreadySelected = newSelectedMembers.some(
        (m) => m.id === student.id && m.type === student.type
      );

      if (!alreadySelected) {
        newSelectedMembers.push(student);
      }
    });

    setSelectedMembers(newSelectedMembers);
  };

  // Add this function to handle deselecting all students
  const handleDeselectAllStudents = () => {
    // Remove all students from the selected members
    const newSelectedMembers = selectedMembers.filter(
      (member) =>
        member.type !== "Student" ||
        !filteredStudents.some((student) => student._id === member.id)
    );

    // Also remove from admins
    const newSelectedAdmins = selectedAdmins.filter(
      (admin) =>
        admin.type !== "Student" ||
        !filteredStudents.some((student) => student._id === admin.id)
    );

    setSelectedMembers(newSelectedMembers);
    setSelectedAdmins(newSelectedAdmins);
  };

  // Add these functions to handle modal closing
  const handleCloseCreateModal = () => {
    setShowCreateModal(false);
    setNewGroupData({ name: "", description: "" });
    setSelectedMembers([]);
    setSelectedAdmins([]);
    setSelectedClass("");
    setFilteredStudents(students);
  };

  const handleCloseManageMembersModal = () => {
    setShowManageMembersModal(false);
    setSelectedMembers([]);
    setSelectedAdmins([]);
    setSelectedClass("");
    setFilteredStudents(students);
  };

  // Handle infinite scrolling
  const handleScroll = () => {
    const container = messagesContainerRef.current;
    if (
      container &&
      container.scrollTop === 0 &&
      hasMore &&
      !localLoadingMore
    ) {
      // Set local loading state
      setLocalLoadingMore(true);

      // Save current scroll height
      const scrollHeight = container.scrollHeight;

      // Load more messages
      loadMoreMessages();

      // After loading, restore scroll position
      setTimeout(() => {
        if (container) {
          const newScrollHeight = container.scrollHeight;
          container.scrollTop = newScrollHeight - scrollHeight;
          setLocalLoadingMore(false);
        }
      }, 300); // Increased timeout to ensure messages are loaded
    }
  };

  useEffect(() => {
    // Only auto-scroll if we're already at the bottom or if we sent a new message
    const container = messagesContainerRef.current;
    if (container) {
      const isAtBottom =
        container.scrollHeight - container.scrollTop <=
        container.clientHeight + 100;

      // If messages were added at the end (new message sent/received)
      if (messages.length > prevMessagesLength && isAtBottom) {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
      }
    }

    setPrevMessagesLength(messages.length);
  }, [messages]);

  return (
    <Box
      sx={{
        height: "calc(100vh - 100px)",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Typography variant="h5" sx={{ mb: 2 }}>
        Chat
      </Typography>

      <Grid container spacing={2} sx={{ flexGrow: 1 }}>
        {/* Chat Groups List */}
        <Grid item xs={3}>
          <Paper sx={{ height: "100%", overflow: "auto" }}>
            <Box
              sx={{
                p: 1,
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Typography variant="h6">Groups</Typography>
              <Button
                startIcon={<AddIcon />}
                size="small"
                variant="contained"
                onClick={() => setShowCreateModal(true)}
              >
                New
              </Button>
            </Box>
            <Divider />
            <List>
              {chatGroups.map((group) => (
                <ListItem
                  key={group._id}
                  button
                  selected={currentGroup === group._id}
                  onClick={() => joinChatRoom(group._id)}
                  secondaryAction={
                    <IconButton
                      edge="end"
                      onClick={(e) => {
                        e.stopPropagation();
                        setConfirmDelete({ open: true, groupId: group._id });
                      }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  }
                >
                  <ListItemAvatar>
                    <Badge
                      badgeContent={unreadCounts[group._id] || 0}
                      color="error"
                      invisible={
                        !unreadCounts[group._id] || unreadCounts[group._id] <= 0
                      }
                      sx={{
                        ".MuiBadge-badge": {
                          fontSize: "0.8rem",
                          height: "20px",
                          minWidth: "20px",
                        },
                      }}
                    >
                      <Avatar>
                        <GroupIcon />
                      </Avatar>
                    </Badge>
                  </ListItemAvatar>
                  <ListItemText
                    primary={group.name}
                    secondary={`${group.members.length} members`}
                  />
                </ListItem>
              ))}
              {chatGroups.length === 0 && (
                <ListItem>
                  <ListItemText primary="No chat groups yet" />
                </ListItem>
              )}
            </List>
          </Paper>
        </Grid>

        {/* Chat Messages */}
        <Grid item xs={9}>
          <Paper
            sx={{
              height: "100%",
              display: "flex",
              flexDirection: "column",
              maxHeight: "calc(100vh - 120px)", // Ensure the paper has a max height
            }}
          >
            {currentGroup ? (
              <>
                <Box
                  sx={{
                    p: 2,
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    borderBottom: 1,
                    borderColor: "divider",
                  }}
                >
                  <Typography variant="h6">
                    {currentGroupDetails?.name || "Loading..."}
                  </Typography>
                  <Button
                    startIcon={<EditIcon />}
                    size="small"
                    onClick={openManageMembersModal}
                  >
                    Manage Members
                  </Button>
                </Box>

                {/* Messages container with fixed height */}
                <Box
                  ref={messagesContainerRef}
                  sx={{
                    flexGrow: 1,
                    overflow: "auto",
                    p: 2,
                    height: "calc(100% - 120px)", // Ensure the box has a fixed height
                    display: "flex",
                    flexDirection: "column",
                  }}
                  onScroll={handleScroll}
                >
                  {(localLoadingMore || loadingMore) && (
                    <Box
                      sx={{ display: "flex", justifyContent: "center", mb: 2 }}
                    >
                      <CircularProgress size={24} />
                    </Box>
                  )}

                  {messages.map((message, index) => (
                    <Box
                      key={message._id || message.tempId || index}
                      sx={{
                        display: "flex",
                        justifyContent:
                          message.senderId === user.id
                            ? "flex-end"
                            : "flex-start",
                        mb: 2,
                      }}
                    >
                      <Card
                        sx={{
                          maxWidth: "70%",
                          bgcolor:
                            message.senderId === user.id
                              ? "primary.light"
                              : "grey.100",
                        }}
                      >
                        <CardContent
                          sx={{ py: 1, px: 2, "&:last-child": { pb: 1 } }}
                        >
                          <Typography variant="caption" color="text.secondary">
                            {message.senderInfo?.name || "Unknown"}
                          </Typography>
                          <Typography variant="body1">
                            {message.content}
                          </Typography>
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{ display: "block", textAlign: "right" }}
                          >
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Box>
                  ))}

                  {messages.length === 0 && (
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        height: "100%",
                      }}
                    >
                      <Typography color="text.secondary">
                        No messages yet
                      </Typography>
                    </Box>
                  )}

                  {/* Invisible element to scroll to */}
                  <div ref={messagesEndRef} />
                </Box>

                {/* Message Input */}
                <Box sx={{ p: 2, borderTop: 1, borderColor: "divider" }}>
                  <form onSubmit={handleSendMessage}>
                    <Box sx={{ display: "flex" }}>
                      <TextField
                        id="chat-message-input"
                        fullWidth
                        placeholder="Type a message"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        variant="outlined"
                        size="small"
                      />
                      <Button
                        type="submit"
                        variant="contained"
                        sx={{ ml: 1 }}
                        disabled={!newMessage.trim()}
                      >
                        <SendIcon />
                      </Button>
                    </Box>
                  </form>
                </Box>
              </>
            ) : (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  height: "100%",
                }}
              >
                <Typography color="text.secondary">
                  Select a chat group to start messaging
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Create Group Modal */}
      <Dialog
        open={showCreateModal}
        onClose={handleCloseCreateModal}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Create New Chat Group</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="Group Name"
              value={newGroupData.name}
              onChange={(e) =>
                setNewGroupData({ ...newGroupData, name: e.target.value })
              }
              margin="normal"
            />
            <TextField
              fullWidth
              label="Description"
              value={newGroupData.description}
              onChange={(e) =>
                setNewGroupData({
                  ...newGroupData,
                  description: e.target.value,
                })
              }
              margin="normal"
              multiline
              rows={2}
            />

            <Typography variant="h6" sx={{ mt: 2 }}>
              Add Members
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="subtitle1">Teachers</Typography>
                <List sx={{ maxHeight: 200, overflow: "auto" }}>
                  {teachers.map((teacher) => (
                    <ListItem key={teacher._id}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={selectedMembers.some(
                              (m) =>
                                m.id === teacher._id && m.type === "Teacher"
                            )}
                            onChange={() =>
                              handleMemberSelection(teacher._id, "Teacher")
                            }
                          />
                        }
                        label={teacher.name}
                      />
                      {selectedMembers.some(
                        (m) => m.id === teacher._id && m.type === "Teacher"
                      ) && (
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={selectedAdmins.some(
                                (a) =>
                                  a.id === teacher._id && a.type === "Teacher"
                              )}
                              onChange={() =>
                                handleAdminSelection(teacher._id, "Teacher")
                              }
                            />
                          }
                          label="Admin"
                        />
                      )}
                    </ListItem>
                  ))}
                </List>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle1">Students</Typography>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    mb: 1,
                  }}
                >
                  <FormControl sx={{ width: "70%" }}>
                    <InputLabel>Filter by Class</InputLabel>
                    <Select
                      value={selectedClass}
                      onChange={handleClassChange}
                      label="Filter by Class"
                    >
                      <MenuItem value="">All Classes</MenuItem>
                      {classes.map((cls) => (
                        <MenuItem key={cls._id} value={cls._id}>
                          {cls.class_text} ({cls.class_num})
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => {
                      const allSelected = filteredStudents.every((student) =>
                        selectedMembers.some(
                          (m) => m.id === student._id && m.type === "Student"
                        )
                      );
                      if (allSelected) {
                        handleDeselectAllStudents();
                      } else {
                        handleSelectAllStudents();
                      }
                    }}
                  >
                    {filteredStudents.every((student) =>
                      selectedMembers.some(
                        (m) => m.id === student._id && m.type === "Student"
                      )
                    )
                      ? "Deselect All"
                      : "Select All"}
                  </Button>
                </Box>
                <List sx={{ maxHeight: 200, overflow: "auto" }}>
                  {filteredStudents.map((student) => (
                    <ListItem key={student._id}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={selectedMembers.some(
                              (m) =>
                                m.id === student._id && m.type === "Student"
                            )}
                            onChange={() =>
                              handleMemberSelection(student._id, "Student")
                            }
                          />
                        }
                        label={`${student.name} ${
                          student.student_class
                            ? `(${student.student_class.class_text})`
                            : ""
                        }`}
                      />
                      {selectedMembers.some(
                        (m) => m.id === student._id && m.type === "Student"
                      ) && (
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={selectedAdmins.some(
                                (a) =>
                                  a.id === student._id && a.type === "Student"
                              )}
                              onChange={() =>
                                handleAdminSelection(student._id, "Student")
                              }
                            />
                          }
                          label="Admin"
                        />
                      )}
                    </ListItem>
                  ))}
                </List>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseCreateModal}>Cancel</Button>
          <Button
            onClick={handleCreateGroup}
            variant="contained"
            disabled={!newGroupData.name || selectedMembers.length === 0}
          >
            Create Group
          </Button>
        </DialogActions>
      </Dialog>

      {/* Manage Members Modal */}
      <Dialog
        open={showManageMembersModal}
        onClose={handleCloseManageMembersModal}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Manage Group Members</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Typography variant="h6">
              Current Group: {currentGroupDetails?.name}
            </Typography>

            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={6}>
                <Typography variant="subtitle1">Teachers</Typography>
                <List sx={{ maxHeight: 200, overflow: "auto" }}>
                  {teachers.map((teacher) => (
                    <ListItem key={teacher._id}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={selectedMembers.some(
                              (m) =>
                                m.id === teacher._id && m.type === "Teacher"
                            )}
                            onChange={() =>
                              handleMemberSelection(teacher._id, "Teacher")
                            }
                          />
                        }
                        label={teacher.name}
                      />
                      {selectedMembers.some(
                        (m) => m.id === teacher._id && m.type === "Teacher"
                      ) && (
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={selectedAdmins.some(
                                (a) =>
                                  a.id === teacher._id && a.type === "Teacher"
                              )}
                              onChange={() =>
                                handleAdminSelection(teacher._id, "Teacher")
                              }
                            />
                          }
                          label="Admin"
                        />
                      )}
                    </ListItem>
                  ))}
                </List>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle1">Students</Typography>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    mb: 1,
                  }}
                >
                  <FormControl sx={{ width: "70%" }}>
                    <InputLabel>Filter by Class</InputLabel>
                    <Select
                      value={selectedClass}
                      onChange={handleClassChange}
                      label="Filter by Class"
                    >
                      <MenuItem value="">All Classes</MenuItem>
                      {classes.map((cls) => (
                        <MenuItem key={cls._id} value={cls._id}>
                          {cls.class_text} ({cls.class_num})
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => {
                      const allSelected = filteredStudents.every((student) =>
                        selectedMembers.some(
                          (m) => m.id === student._id && m.type === "Student"
                        )
                      );
                      if (allSelected) {
                        handleDeselectAllStudents();
                      } else {
                        handleSelectAllStudents();
                      }
                    }}
                  >
                    {filteredStudents.every((student) =>
                      selectedMembers.some(
                        (m) => m.id === student._id && m.type === "Student"
                      )
                    )
                      ? "Deselect All"
                      : "Select All"}
                  </Button>
                </Box>
                <List sx={{ maxHeight: 200, overflow: "auto" }}>
                  {filteredStudents.map((student) => (
                    <ListItem key={student._id}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={selectedMembers.some(
                              (m) =>
                                m.id === student._id && m.type === "Student"
                            )}
                            onChange={() =>
                              handleMemberSelection(student._id, "Student")
                            }
                          />
                        }
                        label={`${student.name} ${
                          student.student_class
                            ? `(${student.student_class.class_text})`
                            : ""
                        }`}
                      />
                      {selectedMembers.some(
                        (m) => m.id === student._id && m.type === "Student"
                      ) && (
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={selectedAdmins.some(
                                (a) =>
                                  a.id === student._id && a.type === "Student"
                              )}
                              onChange={() =>
                                handleAdminSelection(student._id, "Student")
                              }
                            />
                          }
                          label="Admin"
                        />
                      )}
                    </ListItem>
                  ))}
                </List>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseManageMembersModal}>Cancel</Button>
          <Button
            onClick={handleUpdateMembers}
            variant="contained"
            disabled={selectedMembers.length === 0}
          >
            Update Members
          </Button>
        </DialogActions>
      </Dialog>

      {/* Confirm Delete Dialog */}
      <Dialog
        open={confirmDelete.open}
        onClose={() => setConfirmDelete({ open: false, groupId: null })}
      >
        <DialogTitle>Delete Chat Group</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this chat group? This action cannot
            be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setConfirmDelete({ open: false, groupId: null })}
          >
            Cancel
          </Button>
          <Button onClick={handleDeleteGroup} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Message Alert */}
      {messageAlert.open && (
        <MessageSnackbar
          message={messageAlert.message}
          type={messageAlert.type}
          handleClose={() => setMessageAlert({ ...messageAlert, open: false })}
        />
      )}
    </Box>
  );
};

export default Chat;
