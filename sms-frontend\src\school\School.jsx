import * as React from "react";
import { styled, useTheme } from "@mui/material/styles";
import Box from "@mui/material/Box";
import MuiDrawer from "@mui/material/Drawer";
import MuiAppBar from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import List from "@mui/material/List";
import CssBaseline from "@mui/material/CssBaseline";
import Typography from "@mui/material/Typography";
import Divider from "@mui/material/Divider";
import IconButton from "@mui/material/IconButton";
import MenuIcon from "@mui/icons-material/Menu";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import ListItem from "@mui/material/ListItem";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import InboxIcon from "@mui/icons-material/MoveToInbox";
import MailIcon from "@mui/icons-material/Mail";
import { Link, Outlet, useNavigate } from "react-router-dom";

// marteial ui icon
import DashboardCustomizeIcon from "@mui/icons-material/DashboardCustomize";
import ManageAccountsIcon from "@mui/icons-material/ManageAccounts";
import PeopleIcon from "@mui/icons-material/People";
import SchoolIcon from "@mui/icons-material/School";
import ClassIcon from "@mui/icons-material/Class";
import EventIcon from "@mui/icons-material/Event";
import NotificationsIcon from "@mui/icons-material/Notifications";
import SubjectIcon from "@mui/icons-material/Subject";
import ExplicitIcon from "@mui/icons-material/Explicit";
import BookmarksIcon from "@mui/icons-material/Bookmarks";
import HomeIcon from "@mui/icons-material/Home";
import LogoutIcon from "@mui/icons-material/Logout";
import QrCodeScannerIcon from "@mui/icons-material/QrCodeScanner";

const drawerWidth = 240;

const openedMixin = (theme) => ({
  width: drawerWidth,
  transition: theme.transitions.create("width", {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  overflowX: "hidden",
});

const closedMixin = (theme) => ({
  transition: theme.transitions.create("width", {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  overflowX: "hidden",
  width: `calc(${theme.spacing(7)} + 1px)`,
  [theme.breakpoints.up("sm")]: {
    width: `calc(${theme.spacing(8)} + 1px)`,
  },
});

const DrawerHeader = styled("div")(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "flex-end",
  padding: theme.spacing(0, 1),
  // necessary for content to be below app bar
  ...theme.mixins.toolbar,
}));

const AppBar = styled(MuiAppBar, {
  shouldForwardProp: (prop) => prop !== "open",
})(({ theme }) => ({
  zIndex: theme.zIndex.drawer + 1,
  transition: theme.transitions.create(["width", "margin"], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  variants: [
    {
      props: ({ open }) => open,
      style: {
        marginLeft: drawerWidth,
        width: `calc(100% - ${drawerWidth}px)`,
        transition: theme.transitions.create(["width", "margin"], {
          easing: theme.transitions.easing.sharp,
          duration: theme.transitions.duration.enteringScreen,
        }),
      },
    },
  ],
}));

const Drawer = styled(MuiDrawer, {
  shouldForwardProp: (prop) => prop !== "open",
})(({ theme }) => ({
  width: drawerWidth,
  flexShrink: 0,
  whiteSpace: "nowrap",
  boxSizing: "border-box",
  variants: [
    {
      props: ({ open }) => open,
      style: {
        ...openedMixin(theme),
        "& .MuiDrawer-paper": openedMixin(theme),
      },
    },
    {
      props: ({ open }) => !open,
      style: {
        ...closedMixin(theme),
        "& .MuiDrawer-paper": closedMixin(theme),
      },
    },
  ],
}));

export default function School() {
  const theme = useTheme();
  const [open, setOpen] = React.useState(false);
  const [user, setUser] = React.useState(null);

  React.useEffect(() => {
    const userStr = localStorage.getItem("user");
    if (userStr) {
      setUser(JSON.parse(userStr));
    }
  }, []);

  console.log(user?.role);

  const handleDrawerOpen = () => {
    setOpen(true);
  };

  const handleDrawerClose = () => {
    setOpen(false);
  };
  if (user?.role == "SCHOOL") {
    var navArr = [
      {
        link: "/",
        component: "Home",
        icon: HomeIcon,
      },
      {
        link: "/school",
        component: "Dashboard",
        icon: DashboardCustomizeIcon,
      },
      {
        link: "/school/class",
        component: "Class",
        icon: ClassIcon,
      },
      {
        link: "/school/subjects",
        component: "Subjects",
        icon: SubjectIcon,
      },
      {
        link: "/school/students",
        component: "Students",
        icon: PeopleIcon,
      },
      {
        link: "/school/teachers",
        component: "Teachers",
        icon: ManageAccountsIcon,
      },
      {
        link: "/school/scheduel",
        component: "Scheduel",
        icon: EventIcon,
      },
      {
        link: "/school/attendance",
        component: "Attendance",
        icon: BookmarksIcon,
      },
      {
        link: "/school/examinations",
        component: "Examinations",
        icon: ExplicitIcon,
      },
      { link: "/school/notice", component: "Notice", icon: NotificationsIcon },
      {
        link: "/school/file-upload",
        component: "File Upload",
        icon: NotificationsIcon,
      },
      { link: "/logout", component: "Log Out", icon: LogoutIcon },
    ];
  } else {
    var navArr = [
      {
        link: "/",
        component: "Home",
        icon: HomeIcon,
      },
      {
        link: "/school",
        component: "Dashboard",
        icon: DashboardCustomizeIcon,
      },
      {
        link: "/school/class",
        component: "Class",
        icon: ClassIcon,
      },
      {
        link: "/school/subjects",
        component: "Subjects",
        icon: SubjectIcon,
      },
      {
        link: "/school/students",
        component: "Students",
        icon: PeopleIcon,
      },
      {
        link: "/school/teachers",
        component: "Teachers",
        icon: ManageAccountsIcon,
      },
      {
        link: "/school/scheduel",
        component: "Scheduel",
        icon: EventIcon,
      },
      {
        link: "/school/QR-base-attendance",
        component: "Attendance",
        icon: BookmarksIcon,
      },
      {
        link: "/school/QR-Scan",
        component: "QR-SCAN",
        icon: QrCodeScannerIcon,
      },
      {
        link: "/school/examinations",
        component: "Examinations",
        icon: ExplicitIcon,
      },
      { link: "/school/notice", component: "Notice", icon: NotificationsIcon },
      {
        link: "/school/file-upload",
        component: "File Upload",
        icon: NotificationsIcon,
      },
      { link: "/logout", component: "Log Out", icon: LogoutIcon },
    ];
  }
  const naviagte = useNavigate();
  const handleNavigation = (link) => {
    naviagte(link);
  };

  return (
    <Box sx={{ display: "flex" }}>
      <CssBaseline />
      <AppBar position="fixed" open={open}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            onClick={handleDrawerOpen}
            edge="start"
            sx={[
              {
                marginRight: 5,
              },
              open && { display: "none" },
            ]}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div">
            School Management System
          </Typography>
        </Toolbar>
      </AppBar>
      <Drawer variant="permanent" open={open}>
        <DrawerHeader>
          <IconButton onClick={handleDrawerClose}>
            {theme.direction === "rtl" ? (
              <ChevronRightIcon />
            ) : (
              <ChevronLeftIcon />
            )}
          </IconButton>
        </DrawerHeader>
        <Divider />
        <List>
          {navArr.map((navItem, index) => (
            <ListItem key={index} disablePadding sx={{ display: "block" }}>
              <ListItemButton
                sx={[
                  {
                    minHeight: 48,
                    px: 2.5,
                  },
                  open
                    ? {
                        justifyContent: "initial",
                      }
                    : {
                        justifyContent: "center",
                      },
                ]}
                onClick={() => {
                  handleNavigation(navItem.link);
                }}
              >
                <ListItemIcon
                  sx={[
                    {
                      minWidth: 0,
                      justifyContent: "center",
                    },
                    open
                      ? {
                          mr: 3,
                        }
                      : {
                          mr: "auto",
                        },
                  ]}
                >
                  {<navItem.icon />}
                </ListItemIcon>
                <ListItemText
                  primary={navItem.component}
                  sx={[
                    open
                      ? {
                          opacity: 1,
                        }
                      : {
                          opacity: 0,
                        },
                  ]}
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
        <Divider />
      </Drawer>
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <DrawerHeader />
        <Outlet />
      </Box>
    </Box>
  );
}
