import * as React from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import TimePicker from "react-time-picker";
import "react-time-picker/dist/TimePicker.css";
import "react-clock/dist/Clock.css";

import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  selectClasses,
  TextField,
  Typography,
} from "@mui/material";
import { useFormik } from "formik";
import { examinationSchema } from "../../../yupSchema/examinationSchema";
import dayjs from "dayjs";
import axios from "axios";
import { server } from "../../../server";
import MessageSnackbar from "../../../basic utility component/snakebar/MessageSnakebar";
// react calender
import { Calendar, momentLocalizer } from "react-big-calendar";
import moment from "moment";
import "react-big-calendar/lib/css/react-big-calendar.css";

// react calender

export default function StudentExaminations() {
  const [examinations, setExaminations] = React.useState([]);
  const [classes, setClasses] = React.useState([]);
  const [calendarEvents, setCalendarEvents] = React.useState([]);
  const [selectedClass, setSelectedClass] = React.useState("");
  const [calendarView, setCalendarView] = React.useState("week");
  const [calendarDate, setCalendarDate] = React.useState(new Date());

  const fetchClasses = () => {
    axios
      .get(`${server}/api/class/all`)
      .then((response) => {
        setClasses(response.data.data);
      })
      .catch((e) => {
        console.log("Error in fetching class.");
      });
  };

  const fetchExaminations = async () => {
    try {
      if (selectedClass) {
        const response = await axios.get(
          `${server}/api/examination/class/${selectedClass}`
        );
        console.log(response.data.examinations);
        setExaminations(response.data.examinations);
      } else {
        const response = await axios.get(`${server}/api/examination/all`);
        console.log(response.data.examinations);
        setExaminations(response.data.examinations);
      }
    } catch (error) {
      console.log(
        "ERROR--> (Saving Fetching Examination- Examination Component",
        error
      );
    }
  };
  React.useEffect(() => {
    fetchExaminations();
  }, [selectedClass]);

  React.useEffect(() => {
    fetchClasses();
  }, []);

  const localizer = momentLocalizer(moment);

  const now = new Date();

  const formatExamsForCalendar = (examData) => {
    return examData.map((item, index) => {
      const examDate = new Date(item.examDate); // e.g., "2025-05-12T19:00:00.000Z"

      const [startHour, startMinute] = item.startTime.split(":");
      const [endHour, endMinute] = item.endTime.split(":");

      const start = new Date(examDate);
      start.setHours(+startHour, +startMinute, 0, 0);

      const end = new Date(examDate);
      end.setHours(+endHour, +endMinute, 0, 0);

      return {
        id: item._id,
        title: `Subject:${item.subject?.subject_name || "title"} Teacher:${
          item.teacher?.name || "teacher"
        } Invigilator:${item.invigilator?.name} Class:${
          item.class?.class_text
        }/${item.class?.class_num}`,

        start,
        end,
      };
    });
  };

  React.useEffect(() => {
    const calendardata = formatExamsForCalendar(examinations);

    setCalendarEvents(calendardata);
    console.log("calender event", calendardata);
  }, [examinations]);

  return (
    <>
      <Paper
        sx={{
          display: "flex",

          marginBottom: "10px",
          justifyContent: "space-between",
        }}
      >
        <Box>
          {/* class */}
          <FormControl sx={{ width: "20vw" }}>
            <InputLabel id="demo-simple-select-label">Class</InputLabel>
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              label="Class"
              name="class"
              onChange={(e) => setSelectedClass(e.target.value)}
              value={selectedClass}
            >
              <MenuItem value={""}>Un Select</MenuItem>
              {classes &&
                classes.map((x) => {
                  return (
                    <MenuItem key={x._id} value={x._id}>
                      {x.class_text} ({x.class_num})
                    </MenuItem>
                  );
                })}
            </Select>
          </FormControl>
        </Box>
      </Paper>

      {selectedClass === "" ? (
        <Typography>Select Class to Show</Typography>
      ) : (
        <div style={{ height: "100vh" }}>
          <Calendar
            localizer={localizer}
            events={calendarEvents}
            startAccessor="start"
            endAccessor="end"
            style={{ height: "100%", margin: "20px" }}
            defaultView={calendarView}
            view={calendarView}
            date={calendarDate}
            min={new Date(1970, 1, 1, 8, 0, 0)}
            max={new Date(1970, 1, 1, 22, 0, 0)}
            onNavigate={(date) => setCalendarDate(date)}
            onView={(view) => setCalendarView(view)}
            sx={{ height: "100%", width: "100%" }}
          />
        </div>
      )}
    </>
  );
}
