import * as React from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";

import { Box, FormControl, InputLabel, MenuItem, Select } from "@mui/material";

import axios from "axios";
import { server } from "../../../server";

export default function TeacherExaminations() {
  const [examinations, setExaminations] = React.useState([]);
  const [classes, setClasses] = React.useState([]);

  const [selectedClass, setSelectedClass] = React.useState("");

  const dateformat = (dateData) => {
    const date = new Date(dateData);

    return (
      date.getDate() + "-" + (+date.getMonth() + 1) + "-" + date.getFullYear()
    );
  };

  const fetchClasses = () => {
    axios
      .get(`${server}/api/class/all`)
      .then((response) => {
        setClasses(response.data.data);
      })
      .catch((e) => {
        console.log("Error in fetching class.");
      });
  };

  const fetchExaminations = async () => {
    try {
      if (selectedClass) {
        const response = await axios.get(
          `${server}/api/examination/class/${selectedClass}`
        );
        console.log("by class", response.data.examinations);
        setExaminations(response.data.examinations);
      } else {
        const response = await axios.get(`${server}/api/examination/all`);
        console.log("all", response.data.examinations);
        setExaminations(response.data.examinations);
      }
    } catch (error) {
      console.log(
        "ERROR--> (Saving Fetching Examination- Examination Component",
        error
      );
    }
  };
  React.useEffect(() => {
    fetchExaminations();
  }, [selectedClass]);
  React.useEffect(() => {
    fetchClasses();
  }, []);
  return (
    <>
      <Box
        sx={{
          display: "flex",
          marginBottom: "10px",
          justifyContent: "space-between",
        }}
      >
        {/* class */}
        <FormControl sx={{ marginTop: "20px", width: "20vw" }}>
          <InputLabel id="demo-simple-select-label">Class</InputLabel>
          <Select
            labelId="demo-simple-select-label"
            id="demo-simple-select"
            label="Class"
            name="class"
            onChange={(e) => setSelectedClass(e.target.value)}
            value={selectedClass}
          >
            <MenuItem value={""}>Un Select</MenuItem>
            {classes &&
              classes.map((x) => {
                return (
                  <MenuItem key={x._id} value={x._id}>
                    {x.class_text} ({x.class_num})
                  </MenuItem>
                );
              })}
          </Select>
        </FormControl>
      </Box>

      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 650 }} aria-label="simple table">
          <TableHead>
            <TableRow>
              <TableCell>Exam Date</TableCell>
              <TableCell>Exam Time</TableCell>
              <TableCell>Subject</TableCell>
              <TableCell>Class</TableCell>
              <TableCell>Exam Type</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {examinations.map((examination) => (
              <TableRow
                key={examination._id}
                sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
              >
                <TableCell>{dateformat(examination.examDate)}</TableCell>
                <TableCell>{examination.examTime}</TableCell>
                <TableCell>{examination.subject.subject_name}</TableCell>
                <TableCell>{examination.class.class_text}</TableCell>
                <TableCell>{examination.examType}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
}
