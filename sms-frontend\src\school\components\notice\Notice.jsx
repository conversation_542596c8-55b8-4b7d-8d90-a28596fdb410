import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import { useFormik } from "formik";
import { noticeSchema } from "../../../yupSchema/noticeSchema";
import { server } from "../../../server";
import axios from "axios";
import { useEffect, useState } from "react";

// material ui icon
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import MessageSnackbar from "../../../basic utility component/snakebar/MessageSnakebar";

export default function Notice() {
  const [message, setMessage] = useState("");
  const [messageType, setMessageType] = useState("success");
  const handleMessageClose = () => {
    setMessage("");
  };
  const [notices, setNotices] = useState([]);
  const [edit, setEdit] = useState(false);
  const [editId, setEditId] = useState(null);
  console.log(notices);
  const handleEdit = (id, title, message, audience) => {
    console.log(id);
    setEdit(true);
    setEditId(id);
    Formik.setFieldValue("title", title);
    Formik.setFieldValue("message", message);
    Formik.setFieldValue("audience", audience);
  };
  const cancelEdit = () => {
    setEdit(false);
    setEditId(null);
    Formik.resetForm();
  };

  const handleDelete = (id) => {
    console.log(id);
    axios
      .delete(`${server}/api/notice/delete/${id}`)
      .then((response) => {
        setMessage(response.data.message);
        setMessageType("success");
      })
      .catch((e) => {
        console.log(e);
        setMessage("Error in delete.");
        setMessageType("error");
      });
  };
  const Formik = useFormik({
    initialValues: {
      title: "",
      message: "",
      audience: "",
    },
    validationSchema: noticeSchema,
    onSubmit: (values) => {
      console.log(values);
      if (edit) {
        axios
          .patch(`${server}/api/notice/update/${editId}`, { ...values })
          .then((response) => {
            cancelEdit();
            setMessage(response.data.message);
            setMessageType("Success");
          })
          .catch((e) => {
            console.log("Error in updating notice", e);
            setMessage("error in updating Notice");
            setMessageType("error");
          });
        Formik.resetForm();
      } else {
        axios
          .post(`${server}/api/notice/create`, { ...values })
          .then((response) => {
            setMessage(response.data.message);
            setMessageType("Success");
          })
          .catch((e) => {
            console.log("Error in Adding notice", e);
            setMessage("error in saving Notice");
            setMessageType("error");
          });
        Formik.resetForm();
      }
    },
  });
  const fetchAllNotices = () => {
    axios
      .get(`${server}/api/notice/all`)
      .then((response) => {
        setNotices(response.data.data);
      })
      .catch((e) => {
        console.log("Error in fetching all notices", e);
      });
  };

  useEffect(() => {
    fetchAllNotices();
  }, [message]);

  return (
    <>
      {message && (
        <MessageSnackbar
          message={message}
          type={messageType}
          handleClose={handleMessageClose}
        />
      )}
      <h1>Notice</h1>
      <Box
        component="form"
        sx={{
          "& > :not(style)": { m: 1 },
          display: "flex",
          flexDirection: "column",
          width: "60vw",
          minWidth: "230px",
          margin: "auto",
        }}
        noValidate
        autoComplete="off"
        onSubmit={Formik.handleSubmit}
      >
        {edit ? (
          <Typography
            variant="h4"
            sx={{ textAlign: "center", fontWeight: 700 }}
          >
            Edit Notice
          </Typography>
        ) : (
          <Typography
            variant="h4"
            sx={{ textAlign: "center", fontWeight: 700 }}
          >
            Add Notices
          </Typography>
        )}
        <TextField
          name="title"
          label="Title"
          value={Formik.values.title}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.title && Formik.errors.title && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.title}
          </p>
        )}

        <TextField
          multiline
          rows={5}
          name="message"
          label="Message"
          value={Formik.values.message}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.message && Formik.errors.message && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.message}
          </p>
        )}
        <FormControl>
          <InputLabel id="demo-simple-select-label">Audience</InputLabel>
          <Select
            labelId="demo-simple-select-label"
            id="demo-simple-select"
            value={Formik.values.audience}
            label="Audience"
            name="audience"
            onChange={Formik.handleChange}
          >
            <MenuItem value={"student"}>Student</MenuItem>
            <MenuItem value={"teacher"}>Teacher</MenuItem>
          </Select>
        </FormControl>
        {Formik.touched.audience && Formik.errors.audience && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.audience}
          </p>
        )}
        <Button type="submit" variant="contained">
          Save
        </Button>

        {edit && (
          <Button
            onClick={() => {
              cancelEdit();
            }}
            type="button"
            variant="contained"
          >
            Cancel
          </Button>
        )}
      </Box>
      <Box
        component={"div"}
        sx={{ display: "flex", flexDirection: "row", flexWrap: "wrap" }}
      >
        {notices &&
          notices.map((x) => {
            return (
              <Paper key={x._id} sx={{ m: 2, p: 2 }}>
                <Box component={"div"}>
                  <Typography>{x.title}</Typography>
                  <Typography>{x.message}</Typography>
                  <Typography>{x.audience}</Typography>
                </Box>
                <Box component={"div"}>
                  <Button
                    onClick={() => {
                      handleEdit(x._id, x.title, x.message, x.audience);
                    }}
                  >
                    <EditIcon />
                  </Button>
                  <Button
                    onClick={() => {
                      handleDelete(x._id);
                    }}
                    sx={{ color: "red" }}
                  >
                    <DeleteIcon />
                  </Button>
                </Box>
              </Paper>
            );
          })}
      </Box>
    </>
  );
}
