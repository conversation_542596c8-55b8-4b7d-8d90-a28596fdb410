import Box from "@mui/material/Box";
import TextField from "@mui/material/TextField";
import * as React from "react";

import {
  FormControl,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import axios from "axios";

import Grid from "@mui/material/Grid";
import { styled } from "@mui/material/styles";
import { Link } from "react-router-dom";
import MessageSnackbar from "../../../basic utility component/snakebar/MessageSnakebar";
import { server } from "../../../server";

const Item = styled(Paper)(({ theme }) => ({
  backgroundColor: "#fff",
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  color: (theme.vars ?? theme).palette.text.secondary,
  ...theme.applyStyles("dark", {
    backgroundColor: "#1A2027",
  }),
}));

export default function QrAttendanceStudentList() {
  const [editId, setEditId] = React.useState(null);
  const [edit, setEdit] = React.useState(false);
  const [classes, setClasses] = React.useState([]);

  const [message, setMessage] = React.useState("");
  const [messageType, setMessageType] = React.useState("success");
  const handleMessageClose = () => {
    setMessage("");
  };

  const handleMessage = (message, type) => {
    setMessageType(type);
    setMessage(message);
  };

  const [params, setParams] = React.useState({});
  const [students, setStudents] = React.useState([]);
  const [selectedClass, setSelectedClasses] = React.useState(null);
  const handleClass = (e) => {
    setSelectedClasses(e.target.value);
    const value = e.target.value || undefined;
    setParams((prev) => ({ ...prev, student_class: value }));
  };

  const handleSearch = (e) => {
    const value = e.target.value || undefined;
    setParams((prev) => ({ ...prev, search: value }));
  };
  const fetchStudents = () => {
    const cleanParams = {};

    if (params.search && params.search.trim() !== "") {
      cleanParams.search = params.search.trim();
    }

    if (params.student_class && params.student_class !== "undefined") {
      cleanParams.student_class = params.student_class;
    }

    axios
      .get(`${server}/api/student/fetch-with-query`, { params: cleanParams })
      .then((response) => {
        setStudents(response.data.students);
        // fetchAttendanceForStudents(response.data.students);
      })
      .catch((e) => {
        console.log("Error in fetching Students.");
      });
  };

  const fetchClasses = () => {
    axios
      .get(`${server}/api/class/all`)
      .then((response) => {
        setClasses(response.data.data);
      })
      .catch((e) => {
        console.log("Error in fetching class.");
      });
  };
  const [attendanceData, setAttendanceData] = React.useState({});
  const fetchAttendanceForStudents = async (studentList) => {
    const attendancePromises = studentList.map((student) =>
      fetchAttendanceForStudent(student.id)
    );
    const results = await Promise.all(attendancePromises);
    const updatedAttendanceData = {};
    results.forEach(({ studentId, attendancePercentage }) => {
      updatedAttendanceData[studentId] = attendancePercentage;
    });
    setAttendanceData(updatedAttendanceData);
  };

  const fetchAttendanceForStudent = async (studentId) => {
    try {
      const response = await axios.get(`${server}/api/attendance/${studentId}`);
      const attendanceRecords = response.data;
      const totalClasses = attendanceRecords.length;
      const presentCount = attendanceRecords.filter(
        (record) => record.status === "Present"
      ).length;
      const attendancePercentage =
        totalClasses > 0 ? (presentCount / totalClasses) * 100 : 0;
      return { studentId, attendancePercentage };
    } catch (error) {
      console.error(
        `Error fetching attendance for student ${studentId}:`,
        error
      );
      return { studentId, attendancePercentage: 0 };
    }
  };

  React.useEffect(() => {
    fetchClasses();
    fetchStudents();
  }, [message, params, selectedClass]);
  return (
    <Box>
      {message && (
        <MessageSnackbar
          message={message}
          type={messageType}
          handleClose={handleMessageClose}
        />
      )}

      <Typography variant="h2" sx={{ textAlign: "center" }}>
        Student Attendance
      </Typography>

      <Grid container spacing={2}>
        <Grid size={{ xs: 6, md: 4 }}>
          <Item>
            <Box
              component={"div"}
              sx={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "center",
                marginTop: "30px",
              }}
            >
              <TextField
                label="Search"
                onChange={(e) => {
                  handleSearch(e);
                }}
              />
              <FormControl sx={{ width: "230px", marginLeft: "10px" }}>
                <InputLabel id="demo-simple-select-label">
                  Student Class
                </InputLabel>
                <Select
                  label="Student Class"
                  onChange={(e) => {
                    handleClass(e);
                  }}
                >
                  <MenuItem value=""> Un-Select</MenuItem>
                  {classes &&
                    classes.map((x) => {
                      return (
                        <MenuItem key={x._id} value={x._id}>
                          {x.class_text} ({x.class_num})
                        </MenuItem>
                      );
                    })}
                </Select>
              </FormControl>
            </Box>
          </Item>
        </Grid>
        <Grid size={{ xs: 6, md: 8 }}>
          <Item>
            <TableContainer component={Paper}>
              <Table sx={{ minWidth: 650 }} aria-label="simple table">
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell align="right">Gender</TableCell>
                    <TableCell align="right">Guardian's Phone</TableCell>
                    <TableCell align="right">Class</TableCell>
                    <TableCell align="right">Percentage</TableCell>
                    <TableCell align="right">View</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {students &&
                    students.map((student) => (
                      <TableRow
                        key={student._id}
                        sx={{
                          "&:last-child td, &:last-child th": { border: 0 },
                        }}
                      >
                        <TableCell component="th" scope="row">
                          {student.name}
                        </TableCell>
                        <TableCell align="right">{student.gender}</TableCell>
                        <TableCell align="right">
                          {student.guardian_phone}
                        </TableCell>
                        <TableCell align="right">
                          {student.student_class.class_text}
                        </TableCell>
                        <TableCell align="right">
                          {attendanceData[student._id] !== undefined
                            ? `${attendanceData[student._id].toFixed(2)}%`
                            : "No Data"}{" "}
                        </TableCell>
                        <TableCell align="right">
                          <Link
                            to={`/school/QR-base-attendance/${student._id}`}
                          >
                            Details
                          </Link>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Item>
        </Grid>
      </Grid>
    </Box>
  );
}
