import React, { useEffect, useRef, useState } from "react";
import axios from "axios";
import { useFormik } from "formik";
import * as Yup from "yup";
import { server } from "../../../server";
import MessageSnackbar from "../../../basic utility component/snakebar/MessageSnakebar";
import { Box, Button, TextField } from "@mui/material";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";

export default function FileManagement() {
  const [files, setFiles] = useState([]);
  const [storageUsed, setStorageUsed] = useState(0);
  const [message, setMessage] = useState("");
  const [messageType, setMessageType] = useState("success");
  const fileInputRef = useRef(null);

  const fetchFiles = async () => {
    const res = await axios.get(`${server}/api/assets/files`);
    console.log(res.data.files);
    setFiles(res.data.files);
    const total = res.data.files.reduce((acc, f) => acc + f.size, 0);
    setStorageUsed(total);
  };
  const handleDelete = async (id) => {
    try {
      const response = await axios.delete(`${server}/api/assets/file/${id}`);
      setMessage(response.data.message);
      setMessageType("success");
    } catch (error) {
      setMessage("Error in uploading file.");
      setMessageType("error");
    }
  };
  const handleMessageClose = () => {
    setMessage("");
  };
  useEffect(() => {
    fetchFiles();
  }, [message]);

  const formik = useFormik({
    initialValues: {
      name: "",
      file: null,
    },
    validationSchema: Yup.object({
      name: Yup.string().required("Required"),
      file: Yup.mixed()
        .required("A file is required")
        .test(
          "fileSize",
          "File too large",
          (value) => value && value.size <= 5 * 1024 * 1024
        )
        .test(
          "fileType",
          "Unsupported Format",
          (value) =>
            value &&
            [
              "application/pdf",
              "application/msword",
              "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
              "application/vnd.ms-powerpoint",
              "application/vnd.openxmlformats-officedocument.presentationml.presentation",
              "application/vnd.ms-excel",
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
              "image/jpeg",
              "image/png",
            ].includes(value.type)
        ),
    }),
    onSubmit: async (values, { resetForm }) => {
      if (storageUsed + values.file.size > 100 * 1024 * 1024) {
        alert("Storage limit exceeded! Delete some files to upload more.");
        return;
      }

      const formData = new FormData();
      formData.append("name", values.name);
      formData.append("file", values.file);

      try {
        const response = await axios.post(
          `${server}/api/assets/upload`,
          formData
        );
        formik.resetForm();
        if (fileInputRef.current) fileInputRef.current.value = "";
        setMessage(response.data.message);
        setMessageType("success");
      } catch (err) {
        setMessage("Error in uploading file.");
        setMessageType("error");
        alert(err.response?.data?.message || "Upload failed");
      }
    },
  });

  return (
    <>
      {message && (
        <MessageSnackbar
          message={message}
          type={messageType}
          handleClose={handleMessageClose}
        />
      )}
      <div style={{ padding: "2rem" }}>
        <Box sx={{ textAlign: "center", fontSize: "42px" }}>
          File Upload (Max Total Storage: 100MB)
        </Box>

        <Box
          component="form"
          onSubmit={formik.handleSubmit}
          encType="multipart/form-data"
          sx={{ display: "flex", flexDirection: "column", gap: "10px" }}
        >
          <TextField
            type="text"
            name="name"
            label="Name"
            variant="outlined"
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            value={formik.values.name}
          />
          {formik.touched.name && formik.errors.name && (
            <p style={{ color: "red", textTransform: "capitalize" }}>
              {formik.errors.name}
            </p>
          )}

          <TextField
            type="file"
            inputRef={fileInputRef}
            name="file"
            variant="outlined"
            onChange={(event) =>
              formik.setFieldValue("file", event.currentTarget.files[0])
            }
          />
          {formik.touched.file && formik.errors.file && (
            <p style={{ color: "red", textTransform: "capitalize" }}>
              {formik.errors.file}
            </p>
          )}

          <Button variant="contained" type="submit">
            Upload
          </Button>
        </Box>

        <Box sx={{ textAlign: "center", fontSize: "42px" }}>Uploaded Files</Box>
        <p>Total Used: {(storageUsed / 1024 / 1024).toFixed(2)} MB / 100 MB</p>
        <TableContainer component={Paper}>
          <Table sx={{ minWidth: 650 }} aria-label="simple table">
            <TableHead>
              <TableRow>
                <TableCell>
                  <b>File Name</b>
                </TableCell>
                <TableCell align="right">
                  <b>File Size</b>
                </TableCell>
                <TableCell align="right">
                  <b>Action</b>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {files.map((file) => (
                <TableRow
                  key={file._id}
                  sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
                >
                  <TableCell component="th" scope="row">
                    <b>{file.name}</b>
                  </TableCell>
                  <TableCell align="right">
                    ({(file.size / 1024 / 1024).toFixed(2)} MB)
                  </TableCell>
                  <TableCell align="right">
                    <Button
                      variant="contained"
                      onClick={() =>
                        window.open(`${server}/api/assets/file/${file._id}`)
                      }
                    >
                      Download
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={() => handleDelete(file._id)}
                    >
                      Delete
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </div>
    </>
  );
}
