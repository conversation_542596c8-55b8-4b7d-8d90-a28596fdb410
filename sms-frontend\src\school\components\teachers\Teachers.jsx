import * as React from "react";
import Box from "@mui/material/Box";
import TextField from "@mui/material/TextField";
import { useFormik } from "formik";

import {
  <PERSON>ton,
  Card,
  CardActionArea,
  CardContent,
  CardMedia,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import axios from "axios";
import { server } from "../../../server";
import MessageSnackbar from "../../../basic utility component/snakebar/MessageSnakebar";
import {
  teacherEditSchema,
  teacherSchema,
} from "../../../yupSchema/teacherSchema";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import {
  DndContext,
  useSensor,
  useSensors,
  PointerSensor,
  DragOverlay,
  useDroppable,
} from "@dnd-kit/core";
import { restrictToWindowEdges } from "@dnd-kit/modifiers";
import { CSS } from "@dnd-kit/utilities";
import { useSortable } from "@dnd-kit/sortable";

// Gender item component
const GenderItem = ({ id, text, isActive }) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    padding: "8px 12px",
    margin: "4px",
    border: "1px solid #ccc",
    borderRadius: "4px",
    backgroundColor: isActive ? "#e0e0e0" : "#f5f5f5",
    cursor: "grab",
    userSelect: "none",
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      {text}
    </div>
  );
};

// Gender drop zone component
const GenderDropZone = ({ selectedGender }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: "gender-dropzone",
  });

  const genderOptions = {
    male: "Male",
    female: "Female",
    other: "Other",
  };

  const selectedText = selectedGender ? genderOptions[selectedGender] : null;

  return (
    <div
      ref={setNodeRef}
      style={{
        border: `2px dashed ${isOver ? "#4caf50" : "#1976d2"}`,
        borderRadius: "8px",
        padding: "16px",
        minHeight: "60px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: isOver
          ? "rgba(76, 175, 80, 0.1)"
          : selectedGender
          ? "#e3f2fd"
          : "#f5f5f5",
        marginBottom: "16px",
        transition: "all 0.2s ease",
      }}
    >
      {selectedText ? (
        <div
          style={{
            padding: "8px 12px",
            borderRadius: "4px",
            backgroundColor: "#1976d2",
            color: "white",
          }}
        >
          {selectedText}
        </div>
      ) : (
        <Typography color="textSecondary">
          {isOver ? "Drop to select" : "Drag a gender option here"}
        </Typography>
      )}
    </div>
  );
};

// Gender selector with drag and drop
const GenderSelector = ({ value, onChange }) => {
  const [activeId, setActiveId] = React.useState(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    })
  );

  const genderOptions = [
    { id: "male", text: "Male" },
    { id: "female", text: "Female" },
    { id: "other", text: "Other" },
  ];

  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = (event) => {
    const { over, active } = event;
    setActiveId(null);

    if (over && over.id === "gender-dropzone") {
      // Gender was dropped in the dropzone
      onChange({
        target: {
          name: "gender",
          value: active.id,
        },
      });
    } else if (!over && value === active.id) {
      // Gender was dragged out and it was the selected one
      onChange({
        target: {
          name: "gender",
          value: "",
        },
      });
    }
  };

  const handleDragCancel = () => {
    setActiveId(null);
  };

  // Filter out the selected gender from available options
  const availableOptions = genderOptions.filter(
    (option) => option.id !== value
  );

  return (
    <div>
      <Typography variant="subtitle1" sx={{ mb: 1 }}>
        Gender
      </Typography>
      <DndContext
        sensors={sensors}
        modifiers={[restrictToWindowEdges]}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragCancel={handleDragCancel}
      >
        <GenderDropZone selectedGender={value} />

        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          Options{" "}
          {availableOptions.length < genderOptions.length
            ? `(${availableOptions.length} of ${genderOptions.length})`
            : ""}
        </Typography>
        <div style={{ display: "flex", flexWrap: "wrap", maxWidth: "100%" }}>
          {availableOptions.map((option) => (
            <GenderItem
              key={option.id}
              id={option.id}
              text={option.text}
              isActive={activeId === option.id}
            />
          ))}
        </div>

        <DragOverlay>
          {activeId
            ? (() => {
                const activeOption = genderOptions.find(
                  (option) => option.id === activeId
                );
                if (activeOption) {
                  return (
                    <div
                      style={{
                        padding: "8px 12px",
                        borderRadius: "4px",
                        backgroundColor: "#bbdefb",
                        boxShadow: "0 5px 10px rgba(0,0,0,0.2)",
                      }}
                    >
                      {activeOption.text}
                    </div>
                  );
                }
                return null;
              })()
            : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
};

export default function Teachers() {
  const [editId, setEditId] = React.useState(null);
  const [edit, setEdit] = React.useState(false);
  const [classes, setClasses] = React.useState([]);
  const [file, setFile] = React.useState(null);
  const [imageUrl, setImageUrl] = React.useState(null);

  const addImage = (event) => {
    const selectedFile = event.target.files[0];
    if (selectedFile) {
      setImageUrl(URL.createObjectURL(selectedFile));
      setFile(selectedFile);
    }
  };
  const fileInputRef = React.useRef(null);
  const handleClearFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
    setFile(null);
    setImageUrl(null);
  };

  const cancelEdit = () => {
    setEdit(false);
    Formik.resetForm();
    setEditId(null);
  };

  const handleDelete = (id) => {
    if (confirm("Are your sure want to delete this teacher")) {
      axios
        .delete(`${server}/api/teacher/delete/${id}`)
        .then((response) => {
          console.log("Success:", response.data);
          setMessage(response.data.message);
          setMessageType("success");
        })
        .catch((e) => {
          setMessage("Error in deleteing the teacher");
          setMessageType("error");
          console.log("Error", e);
        });
    }
  };
  const handleEdit = (id) => {
    setEdit(true);
    setEditId(id);
    const filteredTeacher = teachers.filter((x) => x._id === id);
    console.log("filtered teacher", filteredTeacher);

    Formik.setFieldValue("email", filteredTeacher[0].email);
    Formik.setFieldValue("name", filteredTeacher[0].name);
    Formik.setFieldValue("age", filteredTeacher[0].age);
    Formik.setFieldValue("gender", filteredTeacher[0].gender);
    Formik.setFieldValue("qualification", filteredTeacher[0].qualification);
  };

  const initialValues = {
    email: "",
    name: "",
    age: "",
    gender: "",
    qualification: "",
    password: "",
    confirm_password: "",
  };

  const Formik = useFormik({
    initialValues,
    validationSchema: edit ? teacherEditSchema : teacherSchema,
    onSubmit: (values) => {
      console.log("Register submit values", values);
      if (edit) {
        const fd = new FormData();
        fd.append("name", values.name);
        fd.append("email", values.email);
        fd.append("age", values.age);
        fd.append("gender", values.gender);
        fd.append("qualification", values.qualification);
        if (file) {
          fd.append("image", file, file.name);
        }
        if (values.password) {
          fd.append("password", values.password);
        }
        axios
          .patch(`${server}/api/teacher/update/${editId}`, fd)
          .then((response) => {
            console.log("Success:", response.data);
            setMessage(response.data.message);
            setMessageType("success");
            Formik.resetForm();
            handleClearFile();
            setEdit(false);
          })
          .catch((e) => {
            setMessage("Error in Creating Teacher");
            setMessageType("error");
            console.log("Error", e);
          });
      } else {
        if (file) {
          const fd = new FormData();
          fd.append("image", file, file.name);
          fd.append("name", values.name);
          fd.append("email", values.email);
          fd.append("age", values.age);
          fd.append("gender", values.gender);
          fd.append("qualification", values.qualification);
          fd.append("password", values.password);

          axios
            .post(`${server}/api/teacher/register`, fd)
            .then((response) => {
              console.log("Success:", response.data);
              setMessage(response.data.message);
              setMessageType("success");
              Formik.resetForm();
              handleClearFile();
            })
            .catch((e) => {
              setMessage("Error in Creating Teacher");
              setMessageType("error");
              console.log("Error", e);
            });
        } else {
          setMessage("Please Select School an Image to register");
          setMessageType("error");
        }
      }
    },
  });
  const [message, setMessage] = React.useState("");
  const [messageType, setMessageType] = React.useState("success");
  const handleMessageClose = () => {
    setMessage("");
  };

  const [params, setParams] = React.useState({});
  const [teachers, setTeachers] = React.useState([]);
  const handleClass = (e) => {
    const value = e.target.value || undefined;
    setParams((prev) => ({ ...prev, teacher_class: value }));
  };

  const handleSearch = (e) => {
    const value = e.target.value || undefined;
    setParams((prev) => ({ ...prev, search: value }));
  };
  const fetchTeachers = () => {
    const cleanParams = {};

    if (params.search && params.search.trim() !== "") {
      cleanParams.search = params.search.trim();
    }

    if (params.teacher_class && params.teacher_class !== "undefined") {
      cleanParams.teacher_class = params.teacher_class;
    }

    axios
      .get(`${server}/api/teacher/fetch-with-query`, { params: cleanParams })
      .then((response) => {
        setTeachers(response.data.teachers);
      })
      .catch((e) => {
        console.log("Error in fetching Teachers.");
      });
  };

  const fetchClasses = () => {
    axios
      .get(`${server}/api/class/all`)
      .then((response) => {
        setClasses(response.data.data);
      })
      .catch((e) => {
        console.log("Error in fetching class.");
      });
  };

  React.useEffect(() => {
    fetchClasses();
    fetchTeachers();
  }, [message, params]);
  return (
    <Box>
      {message && (
        <MessageSnackbar
          message={message}
          type={messageType}
          handleClose={handleMessageClose}
        />
      )}

      <Box
        component="form"
        sx={{
          "& > :not(style)": { m: 1 },
          display: "flex",
          flexDirection: "column",
          width: "60vw",
          minWidth: "230px",
          margin: "auto",
        }}
        noValidate
        autoComplete="off"
        onSubmit={Formik.handleSubmit}
      >
        <Typography>Add Teacher Image</Typography>
        <TextField
          type="file"
          inputRef={fileInputRef}
          onChange={(event) => {
            addImage(event);
          }}
        />
        {imageUrl && (
          <Box>
            <CardMedia component="img" image={imageUrl} />
          </Box>
        )}
        <TextField
          name="name"
          label="Name"
          value={Formik.values.name}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.name && Formik.errors.name && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.name}
          </p>
        )}

        <TextField
          name="email"
          label="Email"
          value={Formik.values.email}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.email && Formik.errors.email && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.email}
          </p>
        )}

        <TextField
          name="age"
          label="Age "
          value={Formik.values.age}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.age && Formik.errors.age && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.age}
          </p>
        )}

        <GenderSelector
          value={Formik.values.gender}
          onChange={Formik.handleChange}
        />
        {Formik.touched.gender && Formik.errors.gender && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.gender}
          </p>
        )}

        <TextField
          name="qualification"
          label="Qualification"
          value={Formik.values.qualification}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.qualification && Formik.errors.qualification && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.qualification}
          </p>
        )}

        <TextField
          type="password"
          name="password"
          label="Password"
          value={Formik.values.password}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.password && Formik.errors.password && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.password}
          </p>
        )}

        <TextField
          type="password"
          name="confirm_password"
          label="Confirm Password"
          value={Formik.values.confirm_password}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.confirm_password && Formik.errors.confirm_password && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.confirm_password}
          </p>
        )}
        <Button type="submit" variant="contained">
          Register
        </Button>
        {edit && (
          <Button
            onClick={() => {
              cancelEdit();
            }}
            type="submit"
            variant="contained"
          >
            Cancel
          </Button>
        )}
      </Box>
      <Box
        component={"div"}
        sx={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "center",
          marginTop: "30px",
        }}
      >
        <TextField
          label="Search"
          onChange={(e) => {
            handleSearch(e);
          }}
        />
        <FormControl sx={{ width: "230px", marginLeft: "10px" }}>
          <InputLabel id="demo-simple-select-label">Teacher Class</InputLabel>
          <Select
            label="Teacher Class"
            onChange={(e) => {
              handleClass(e);
            }}
          >
            <MenuItem value=""> Un-Select</MenuItem>
            {classes &&
              classes.map((x) => {
                return (
                  <MenuItem key={x._id} value={x._id}>
                    {x.class_text} ({x.class_num})
                  </MenuItem>
                );
              })}
          </Select>
        </FormControl>
      </Box>
      <Box
        component={"div"}
        sx={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "center",
          marginTop: "30px",
        }}
      >
        {teachers &&
          teachers.map((teacher) => {
            return (
              <Card
                key={teacher._id}
                sx={{ maxWidth: 345, marginRight: "10px" }}
              >
                <CardActionArea>
                  <CardMedia
                    component="img"
                    height="340"
                    image={`/images/uploaded/teacher/${teacher.teacher_image}`}
                    alt="green iguana"
                  />
                  <CardContent>
                    <Typography gutterBottom variant="h5" component="div">
                      <span>Name:</span> {teacher.name}
                    </Typography>
                    <Typography gutterBottom variant="h5" component="div">
                      <span>Email:</span>
                      {teacher.email}
                    </Typography>

                    <Typography gutterBottom variant="h5" component="div">
                      <span>Age:</span>
                      {teacher.age}
                    </Typography>
                    <Typography gutterBottom variant="h5" component="div">
                      <span>Gender:</span>
                      {teacher.gender}
                    </Typography>
                    <Typography gutterBottom variant="h5" component="div">
                      <span>Qualification:</span> {teacher.qualification}
                    </Typography>

                    <Typography
                      variant="body2"
                      sx={{ color: "text.secondary" }}
                    >
                      Lizards are a widespread group of squamate reptiles, with
                      over 6,000 species, ranging across all continents except
                      Antarctica
                    </Typography>
                  </CardContent>
                  <Button
                    onClick={() => {
                      handleEdit(teacher._id);
                    }}
                  >
                    <EditIcon />
                  </Button>
                  <Button
                    onClick={() => {
                      handleDelete(teacher._id);
                    }}
                    sx={{ marginLeft: "18px" }}
                  >
                    <DeleteIcon sx={{ color: "red" }} />
                  </Button>
                </CardActionArea>
              </Card>
            );
          })}
      </Box>
    </Box>
  );
}
