import { useEffect, useRef, useState } from "react";
import { server } from "../../../server";
import axios from "axios";
import { Box, Button, CardMedia, TextField, Typography } from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import MessageSnackbar from "../../../basic utility component/snakebar/MessageSnakebar";

export default function Dashboard() {
  const [school, setSchool] = useState(null);
  const [schoolName, setSchoolName] = useState(null);
  const [edit, setEdit] = useState(false);

  // image handling
  const [file, setFile] = useState(null);
  const [imageUrl, setImageUrl] = useState(null);

  const addImage = (event) => {
    const selectedFile = event.target.files[0];
    if (selectedFile) {
      setImageUrl(URL.createObjectURL(selectedFile));
      setFile(selectedFile);
    }
  };
  const fileInputRef = useRef(null);
  const handleClearFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
    setFile(null);
    setImageUrl(null);
  };
  const [message, setMessage] = useState("");
  const [messageType, setMessageType] = useState("success");
  const handleMessageClose = () => {
    setMessage("");
  };
  const handleEdit = () => {
    const fd = new FormData();
    fd.append("school_name", schoolName);
    if (file) {
      fd.append("image", file, file.name);
    }

    axios
      .patch(`${server}/api/school/update`, fd)
      .then((response) => {
        console.log("school edit", response);
        setMessage(response.data.message);
        setMessageType("success");
        handleClearFile();
        setEdit(false);
      })
      .catch((e) => {
        setMessage(e.response.data.message);
        setMessageType("error");
        console.log("Error", e);
      });
  };
  const cancelEdit = () => {
    setEdit(false);
    handleClearFile();
  };
  const fetchSchool = () => {
    axios
      .get(`${server}/api/school/fetch-single`)
      .then((response) => {
        console.log(response);
        setSchool(response.data.school);
        setSchoolName(response.data.school.school_name);
      })
      .catch((e) => {
        console.log("Error", e);
      });
  };
  useEffect(() => {
    fetchSchool();
  }, [message]);
  return (
    <>
      <h1>Dashboard</h1>
      {message && (
        <MessageSnackbar
          message={message}
          type={messageType}
          handleClose={handleMessageClose}
        />
      )}
      {edit && (
        <>
          {" "}
          <Box
            component="form"
            sx={{
              "& > :not(style)": { m: 1 },
              display: "flex",
              flexDirection: "column",
              width: "60vw",
              minWidth: "230px",
              margin: "auto",
            }}
            noValidate
            autoComplete="off"
          >
            <Typography>Add School Image</Typography>
            <TextField
              type="file"
              inputRef={fileInputRef}
              onChange={(event) => {
                addImage(event);
              }}
            />
            {imageUrl && (
              <Box>
                <CardMedia component="img" image={imageUrl} />
              </Box>
            )}
            <TextField
              name="school_name"
              label="School Name"
              value={schoolName}
              onChange={(e) => {
                setSchoolName(e.target.value);
              }}
            />
            <Button variant="contained" onClick={handleEdit}>
              Update
            </Button>
            <Button
              variant="contained"
              onClick={cancelEdit}
              sx={{ background: "red" }}
            >
              Cancel
            </Button>
          </Box>
        </>
      )}
      {school && (
        <Box
          sx={{
            position: "relative",
            height: "500px",
            width: "100%",
            background: `url(/images/uploaded/school/${school.school_image})`,
            backgroundSize: "cover",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Typography variant="h3">{school.school_name}</Typography>
          <Box
            component={"div"}
            sx={{
              position: "absolute",
              bottom: "10px",
              right: "10px",
              height: "50px",

              width: "50px",
            }}
          >
            <Button
              variant="outlined"
              sx={{ background: "#ffff", borderRadius: "25px" }}
              onClick={() => {
                setEdit(true);
              }}
            >
              <EditIcon />
            </Button>
          </Box>
        </Box>
      )}
    </>
  );
}
