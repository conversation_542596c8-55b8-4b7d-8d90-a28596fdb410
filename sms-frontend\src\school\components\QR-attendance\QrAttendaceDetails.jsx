import axios from "axios";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { server } from "../../../server";
import Paper from "@mui/material/Paper";
import Grid from "@mui/material/Grid";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import { PieChart } from "@mui/x-charts/PieChart";
import { styled } from "@mui/material";

const Item = styled(Paper)(({ theme }) => ({
  backgroundColor: "#fff",
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  color: (theme.vars ?? theme).palette.text.secondary,
  ...theme.applyStyles("dark", {
    backgroundColor: "#1A2027",
  }),
}));

export default function QrAttendanceDetails() {
  const [present, setPresent] = useState(0);
  const [absent, setAbsent] = useState(0);
  const [attendanceData, setAttendanceData] = useState([]);
  const [studentInfo, setStudentInfo] = useState(null);
  const studentId = useParams().id;
  const navigate = useNavigate();

  const convertDate = (dateStr) => {
    const date = new Date(dateStr);
    return `${date.getDate()}-${date.getMonth() + 1}-${date.getFullYear()}`;
  };

  const fetchAttendanceData = async () => {
    try {
      const response = await axios.get(
        `${server}/api/Qr-code-attendance/${studentId}`
      );
      const respData = response.data;

      setAttendanceData(respData);

      // Count present & absent
      let presentCount = 0;
      let absentCount = 0;

      respData.forEach((attendance) => {
        if (attendance.status.toLowerCase() === "present") {
          presentCount++;
        } else if (attendance.status.toLowerCase() === "absent") {
          absentCount++;
        }
      });

      setPresent(presentCount);
      setAbsent(absentCount);

      if (respData.length > 0) {
        setStudentInfo(respData[0].student);
      }
    } catch (error) {
      console.log("Error in fetching student attendance:", error);
      navigate("/school/QR-base-attendance");
    }
  };

  useEffect(() => {
    fetchAttendanceData();
  }, []);

  return (
    <>
      <h1>Attendance Details</h1>

      {studentInfo && (
        <Paper sx={{ p: 2, mb: 2 }}>
          <h2>Student Info</h2>
          <p>
            <strong>Name:</strong> {studentInfo.name}
          </p>
          <p>
            <strong>Age:</strong> {studentInfo.age}
          </p>
          <p>
            <strong>Email:</strong> {studentInfo.email}
          </p>
          <p>
            <strong>Guardian:</strong> {studentInfo.guardian}
          </p>
          <p>
            <strong>Phone:</strong> {studentInfo.guardian_phone}
          </p>
        </Paper>
      )}

      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <Item>
            <PieChart
              series={[
                {
                  data: [
                    { id: 0, value: present, label: `Present (${present})` },
                    { id: 1, value: absent, label: `Absent (${absent})` },
                  ],
                },
              ]}
              width={200}
              height={200}
            />
          </Item>
        </Grid>
        <Grid item xs={12} md={6}>
          <Item>
            <TableContainer component={Paper}>
              <Table sx={{ minWidth: 650 }} aria-label="simple table">
                <TableHead>
                  <TableRow>
                    <TableCell>Date</TableCell>
                    <TableCell align="right">Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {attendanceData.map((attendance) => (
                    <TableRow key={attendance._id}>
                      <TableCell component="th" scope="row">
                        {convertDate(attendance.date)}
                      </TableCell>
                      <TableCell align="right">{attendance.status}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Item>
        </Grid>
      </Grid>
    </>
  );
}
