import * as React from "react";
import Box from "@mui/material/Box";
import TextField from "@mui/material/TextField";
import { useFormik } from "formik";
import { QRCode } from "react-qrcode-logo";
import jsPDF from "jspdf";
import ReactDOMServer from "react-dom/server";

import {
  Button,
  Card,
  CardActionArea,
  CardContent,
  CardMedia,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import axios from "axios";
import { server } from "../../../server";
import MessageSnackbar from "../../../basic utility component/snakebar/MessageSnakebar";
import {
  studentEditSchema,
  studentSchema,
} from "../../../yupSchema/studentSchema";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import {
  DndContext,
  useSensor,
  useSensors,
  PointerSensor,
  DragOverlay,
  useDroppable,
} from "@dnd-kit/core";
import { restrictToWindowEdges } from "@dnd-kit/modifiers";
import { CSS } from "@dnd-kit/utilities";
import { useSortable } from "@dnd-kit/sortable";
import {
  SortableContext,
  horizontalListSortingStrategy,
} from "@dnd-kit/sortable";

// Class item component (draggable)
const ClassItem = ({ id, text, num, isActive }) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    padding: "8px 12px",
    margin: "4px",
    border: "1px solid #ccc",
    borderRadius: "4px",
    backgroundColor: isActive ? "#e0e0e0" : "#f5f5f5",
    cursor: "grab",
    userSelect: "none",
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      {text} ({num})
    </div>
  );
};

// Drop zone component with useDroppable hook
const DropZone = ({ selectedClass, classes }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: "dropzone",
  });

  const selectedItem = classes?.find((c) => c._id === selectedClass);

  return (
    <div
      ref={setNodeRef}
      style={{
        border: `2px dashed ${isOver ? "#4caf50" : "#1976d2"}`,
        borderRadius: "8px",
        padding: "16px",
        minHeight: "80px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: isOver
          ? "rgba(76, 175, 80, 0.1)"
          : selectedClass
          ? "#e3f2fd"
          : "#f5f5f5",
        marginBottom: "16px",
        transition: "all 0.2s ease",
      }}
    >
      {selectedItem ? (
        <div
          style={{
            padding: "8px 12px",
            borderRadius: "4px",
            backgroundColor: "#1976d2",
            color: "white",
          }}
        >
          {selectedItem.class_text} ({selectedItem.class_num})
        </div>
      ) : (
        <Typography color="textSecondary">
          {isOver ? "Drop to select" : "Drag a class here to select"}
        </Typography>
      )}
    </div>
  );
};

// Class selector with drag and drop
const ClassSelector = ({ value, onChange, classes }) => {
  const [activeId, setActiveId] = React.useState(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    })
  );

  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = (event) => {
    const { over, active } = event;
    setActiveId(null);

    if (over && over.id === "dropzone") {
      // Class was dropped in the dropzone
      onChange({
        target: {
          name: "student_class",
          value: active.id,
        },
      });
    } else if (!over && value === active.id) {
      // Class was dragged out and it was the selected one
      onChange({
        target: {
          name: "student_class",
          value: "",
        },
      });
    }
  };

  const handleDragCancel = () => {
    setActiveId(null);
  };

  // Filter out the selected class from available classes
  const availableClasses = classes?.filter((c) => c._id !== value) || [];

  return (
    <div>
      <Typography variant="subtitle1" sx={{ mb: 1 }}>
        Student Class
      </Typography>
      <DndContext
        sensors={sensors}
        modifiers={[restrictToWindowEdges]}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragCancel={handleDragCancel}
      >
        <DropZone id="dropzone" selectedClass={value} classes={classes} />

        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          Available Classes{" "}
          {availableClasses.length < classes?.length
            ? `(${availableClasses.length} of ${classes?.length})`
            : ""}
        </Typography>
        <div style={{ display: "flex", flexWrap: "wrap", maxWidth: "100%" }}>
          {availableClasses.map((x) => (
            <ClassItem
              key={x._id}
              id={x._id}
              text={x.class_text}
              num={x.class_num}
              isActive={activeId === x._id}
            />
          ))}
        </div>

        <DragOverlay>
          {activeId
            ? (() => {
                const activeClass = classes?.find((c) => c._id === activeId);
                if (activeClass) {
                  return (
                    <div
                      style={{
                        padding: "8px 12px",
                        borderRadius: "4px",
                        backgroundColor: "#bbdefb",
                        boxShadow: "0 5px 10px rgba(0,0,0,0.2)",
                      }}
                    >
                      {activeClass.class_text} ({activeClass.class_num})
                    </div>
                  );
                }
                return null;
              })()
            : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
};

// Gender item component
const GenderItem = ({ id, text, isActive }) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    padding: "8px 12px",
    margin: "4px",
    border: "1px solid #ccc",
    borderRadius: "4px",
    backgroundColor: isActive ? "#e0e0e0" : "#f5f5f5",
    cursor: "grab",
    userSelect: "none",
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      {text}
    </div>
  );
};

// Gender drop zone component
const GenderDropZone = ({ selectedGender }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: "gender-dropzone",
  });

  const genderOptions = {
    male: "Male",
    female: "Female",
    other: "Other",
  };

  const selectedText = selectedGender ? genderOptions[selectedGender] : null;

  return (
    <div
      ref={setNodeRef}
      style={{
        border: `2px dashed ${isOver ? "#4caf50" : "#1976d2"}`,
        borderRadius: "8px",
        padding: "16px",
        minHeight: "60px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: isOver
          ? "rgba(76, 175, 80, 0.1)"
          : selectedGender
          ? "#e3f2fd"
          : "#f5f5f5",
        marginBottom: "16px",
        transition: "all 0.2s ease",
      }}
    >
      {selectedText ? (
        <div
          style={{
            padding: "8px 12px",
            borderRadius: "4px",
            backgroundColor: "#1976d2",
            color: "white",
          }}
        >
          {selectedText}
        </div>
      ) : (
        <Typography color="textSecondary">
          {isOver ? "Drop to select" : "Drag a gender option here"}
        </Typography>
      )}
    </div>
  );
};

// Gender selector with drag and drop
const GenderSelector = ({ value, onChange }) => {
  const [activeId, setActiveId] = React.useState(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    })
  );

  const genderOptions = [
    { id: "male", text: "Male" },
    { id: "female", text: "Female" },
    { id: "other", text: "Other" },
  ];

  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = (event) => {
    const { over, active } = event;
    setActiveId(null);

    if (over && over.id === "gender-dropzone") {
      // Gender was dropped in the dropzone
      onChange({
        target: {
          name: "gender",
          value: active.id,
        },
      });
    } else if (!over && value === active.id) {
      // Gender was dragged out and it was the selected one
      onChange({
        target: {
          name: "gender",
          value: "",
        },
      });
    }
  };

  const handleDragCancel = () => {
    setActiveId(null);
  };

  // Filter out the selected gender from available options
  const availableOptions = genderOptions.filter(
    (option) => option.id !== value
  );

  return (
    <div>
      <Typography variant="subtitle1" sx={{ mb: 1 }}>
        Gender
      </Typography>
      <DndContext
        sensors={sensors}
        modifiers={[restrictToWindowEdges]}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragCancel={handleDragCancel}
      >
        <GenderDropZone selectedGender={value} />

        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          Options
        </Typography>
        <div style={{ display: "flex", flexWrap: "wrap", maxWidth: "100%" }}>
          {availableOptions.map((option) => (
            <GenderItem
              key={option.id}
              id={option.id}
              text={option.text}
              isActive={activeId === option.id}
            />
          ))}
        </div>

        <DragOverlay>
          {activeId
            ? (() => {
                const activeOption = genderOptions.find(
                  (option) => option.id === activeId
                );
                if (activeOption) {
                  return (
                    <div
                      style={{
                        padding: "8px 12px",
                        borderRadius: "4px",
                        backgroundColor: "#bbdefb",
                        boxShadow: "0 5px 10px rgba(0,0,0,0.2)",
                      }}
                    >
                      {activeOption.text}
                    </div>
                  );
                }
                return null;
              })()
            : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
};

export default function Students() {
  const [editId, setEditId] = React.useState(null);
  const [edit, setEdit] = React.useState(false);
  const [classes, setClasses] = React.useState([]);
  const [file, setFile] = React.useState(null);
  const [imageUrl, setImageUrl] = React.useState(null);
  const [user, setUser] = React.useState(null);
  const qrRefs = React.useRef({});

  console.log(user?.role);
  const addImage = (event) => {
    const selectedFile = event.target.files[0];
    if (selectedFile) {
      setImageUrl(URL.createObjectURL(selectedFile));
      setFile(selectedFile);
    }
  };
  const fileInputRef = React.useRef(null);
  const handleClearFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
    setFile(null);
    setImageUrl(null);
  };

  const cancelEdit = () => {
    setEdit(false);
    Formik.resetForm();
    setEditId(null);
  };

  const handleDelete = (id) => {
    if (confirm("Are your sure want to delete this student")) {
      axios
        .delete(`${server}/api/student/delete/${id}`)
        .then((response) => {
          console.log("Success:", response.data);
          setMessage(response.data.message);
          setMessageType("success");
        })
        .catch((e) => {
          setMessage("Error in deleteing the student");
          setMessageType("error");
          console.log("Error", e);
        });
    }
  };
  const handleEdit = (id) => {
    setEdit(true);
    setEditId(id);
    const filteredStudent = students.filter((x) => x._id === id);
    console.log("filtered student", filteredStudent);

    Formik.setFieldValue("email", filteredStudent[0].email);
    Formik.setFieldValue("name", filteredStudent[0].name);
    Formik.setFieldValue("student_class", filteredStudent[0].student_class._id);
    Formik.setFieldValue("age", filteredStudent[0].age);
    Formik.setFieldValue("gender", filteredStudent[0].gender);
    Formik.setFieldValue("guardian", filteredStudent[0].guardian);
    Formik.setFieldValue("guardian_phone", filteredStudent[0].guardian_phone);
  };

  const initialValues = {
    email: "",
    name: "",
    student_class: "",
    age: "",
    gender: "",
    guardian: "",
    guardian_phone: "",
    password: "",
    confirm_password: "",
  };

  const Formik = useFormik({
    initialValues,
    validationSchema: edit ? studentEditSchema : studentSchema,
    onSubmit: (values) => {
      console.log("Register submit values", values);
      if (edit) {
        const fd = new FormData();
        fd.append("name", values.name);
        fd.append("email", values.email);
        fd.append("age", values.age);
        fd.append("gender", values.gender);
        fd.append("guardian", values.guardian);
        fd.append("guardian_phone", values.guardian_phone);
        fd.append("student_class", values.student_class);

        if (file) {
          fd.append("image", file, file.name);
        }
        if (values.password) {
          fd.append("password", values.password);
        }
        axios
          .patch(`${server}/api/student/update/${editId}`, fd)
          .then((response) => {
            console.log("Success:", response.data);
            setMessage(response.data.message);
            setMessageType("success");
            Formik.resetForm();
            handleClearFile();
            setEdit(false);
          })
          .catch((e) => {
            setMessage("Error in Updating Student");
            setMessageType("error");
            console.log("Error", e);
          });
      } else {
        if (file) {
          const fd = new FormData();
          fd.append("image", file, file.name);
          fd.append("name", values.name);
          fd.append("email", values.email);
          fd.append("age", values.age);
          fd.append("gender", values.gender);
          fd.append("guardian", values.guardian);
          fd.append("guardian_phone", values.guardian_phone);
          fd.append("student_class", values.student_class);
          fd.append("password", values.password);

          axios
            .post(`${server}/api/student/register`, fd)
            .then((response) => {
              console.log("Success:", response.data);
              setMessage(response.data.message);
              setMessageType("success");
              Formik.resetForm();
              handleClearFile();
            })
            .catch((e) => {
              setMessage("Error in Creating Student");
              setMessageType("error");
              console.log("Error", e);
            });
        } else {
          setMessage("Please Select School an Image to register");
          setMessageType("error");
        }
      }
    },
  });
  const [message, setMessage] = React.useState("");
  const [messageType, setMessageType] = React.useState("success");
  const handleMessageClose = () => {
    setMessage("");
  };

  const [params, setParams] = React.useState({});
  const [students, setStudents] = React.useState([]);
  const handleClass = (e) => {
    const value = e.target.value || undefined;
    setParams((prev) => ({ ...prev, student_class: value }));
  };

  const handleSearch = (e) => {
    const value = e.target.value || undefined;
    setParams((prev) => ({ ...prev, search: value }));
  };
  const fetchStudents = () => {
    const cleanParams = {};

    if (params.search && params.search.trim() !== "") {
      cleanParams.search = params.search.trim();
    }

    if (params.student_class && params.student_class !== "undefined") {
      cleanParams.student_class = params.student_class;
    }

    axios
      .get(`${server}/api/student/fetch-with-query`, { params: cleanParams })
      .then((response) => {
        setStudents(response.data.students);
      })
      .catch((e) => {
        console.log("Error in fetching Students.");
      });
  };

  const fetchClasses = () => {
    axios
      .get(`${server}/api/class/all`)
      .then((response) => {
        setClasses(response.data.data);
      })
      .catch((e) => {
        console.log("Error in fetching class.");
      });
  };
  React.useEffect(() => {
    const userStr = localStorage.getItem("user");
    if (userStr) {
      setUser(JSON.parse(userStr));
    }
  }, []);
  React.useEffect(() => {
    fetchClasses();
    fetchStudents();
  }, [message, params]);
  const downloadQR = async (studentId) => {
    const qr_student = students.find((s) => s._id === studentId);
    // console.log(qr_student);
    if (!qr_student) {
      alert("Student not found!");
      return;
    }

    // const canvas = qr_student.qr_id;
    // const qrCodeDataUrl = canvas.toDataURL("image/png");
    const qrContainer = qrRefs.current[studentId];
    const canvas = qrContainer?.querySelector("canvas");

    if (!canvas) {
      alert("QR code canvas not found!");
      return;
    }

    const qrCodeDataUrl = canvas.toDataURL("image/png");

    // Convert image to base64
    const imagePath = `/images/uploaded/student/${qr_student.student_image}`;
    const toBase64 = async (url) => {
      const response = await fetch(url);
      const blob = await response.blob();

      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    };

    const base64Image = await toBase64(imagePath);

    // Now generate PDF
    const doc = new jsPDF();
    doc.setFontSize(22);
    doc.text("Student Card", 22, 20);
    doc.setFontSize(24);
    doc.text("QR code", 136, 20);
    doc.addImage(base64Image, "PNG", 24, 30, 45, 45); // adjust position/size
    doc.setFontSize(12);
    doc.text(`Name: ${qr_student.name}`, 20, 85);
    doc.text(`Email: ${qr_student.email}`, 20, 90);
    doc.text(`Class: ${qr_student.student_class.class_text}`, 20, 95);
    doc.text(`Guardian: ${qr_student.guardian}`, 20, 100);
    doc.text(`Guardian Phone: ${qr_student.guardian_phone}`, 20, 105);
    doc.addImage(qrCodeDataUrl, "PNG", 120, 30, 60, 60);

    doc.save("qr-code.pdf");
  };

  return (
    <Box>
      {message && (
        <MessageSnackbar
          message={message}
          type={messageType}
          handleClose={handleMessageClose}
        />
      )}

      <Box
        component="form"
        sx={{
          "& > :not(style)": { m: 1 },
          display: "flex",
          flexDirection: "column",
          width: "60vw",
          minWidth: "230px",
          margin: "auto",
        }}
        noValidate
        autoComplete="off"
        onSubmit={Formik.handleSubmit}
      >
        <Typography>Add Student Image</Typography>
        <TextField
          type="file"
          inputRef={fileInputRef}
          onChange={(event) => {
            addImage(event);
          }}
        />
        {imageUrl && (
          <Box>
            <CardMedia component="img" image={imageUrl} />
          </Box>
        )}
        <TextField
          name="name"
          label="Name"
          value={Formik.values.name}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.name && Formik.errors.name && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.name}
          </p>
        )}

        <TextField
          name="email"
          label="Email"
          value={Formik.values.email}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.email && Formik.errors.email && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.email}
          </p>
        )}

        <ClassSelector
          value={Formik.values.student_class}
          onChange={Formik.handleChange}
          classes={classes}
        />
        {Formik.touched.student_class && Formik.errors.student_class && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.student_class}
          </p>
        )}
        <TextField
          name="age"
          label="Age "
          value={Formik.values.age}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.age && Formik.errors.age && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.age}
          </p>
        )}

        <GenderSelector
          value={Formik.values.gender}
          onChange={Formik.handleChange}
        />
        {Formik.touched.gender && Formik.errors.gender && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.gender}
          </p>
        )}

        <TextField
          name="guardian"
          label="Guardian Name"
          value={Formik.values.guardian}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.guardian && Formik.errors.guardian && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.guardian}
          </p>
        )}
        <TextField
          name="guardian_phone"
          label="Guardian Phone"
          value={Formik.values.guardian_phone}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.guardian_phone && Formik.errors.guardian_phone && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.guardian_phone}
          </p>
        )}

        <TextField
          type="password"
          name="password"
          label="Password"
          value={Formik.values.password}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.password && Formik.errors.password && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.password}
          </p>
        )}

        <TextField
          type="password"
          name="confirm_password"
          label="Confirm Password"
          value={Formik.values.confirm_password}
          onChange={Formik.handleChange}
          onBlur={Formik.handleBlur}
        />
        {Formik.touched.confirm_password && Formik.errors.confirm_password && (
          <p style={{ color: "red", textTransform: "capitalize" }}>
            {Formik.errors.confirm_password}
          </p>
        )}
        <Button type="submit" variant="contained">
          Register
        </Button>
        {edit && (
          <Button
            onClick={() => {
              cancelEdit();
            }}
            type="submit"
            variant="contained"
          >
            Cancel
          </Button>
        )}
      </Box>
      <Box
        component={"div"}
        sx={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "center",
          marginTop: "30px",
        }}
      >
        <TextField
          label="Search"
          onChange={(e) => {
            handleSearch(e);
          }}
        />
        <FormControl sx={{ width: "230px", marginLeft: "10px" }}>
          <InputLabel id="demo-simple-select-label">Student Class</InputLabel>
          <Select
            label="Student Class"
            onChange={(e) => {
              handleClass(e);
            }}
          >
            <MenuItem value=""> Un-Select</MenuItem>
            {classes &&
              classes.map((x) => {
                return (
                  <MenuItem key={x._id} value={x._id}>
                    {x.class_text} ({x.class_num})
                  </MenuItem>
                );
              })}
          </Select>
        </FormControl>
      </Box>
      <Box
        component={"div"}
        sx={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "center",
          marginTop: "30px",
        }}
      >
        {students &&
          students.map((student) => {
            return (
              <Card
                key={student._id}
                sx={{ maxWidth: 345, marginRight: "10px" }}
              >
                <CardActionArea>
                  <CardMedia
                    component="img"
                    height="340"
                    image={`/images/uploaded/student/${student.student_image}`}
                    alt="green iguana"
                  />
                  <CardContent>
                    <Typography gutterBottom variant="h5" component="div">
                      <span>Name:</span> {student.name}
                    </Typography>
                    <Typography gutterBottom variant="h5" component="div">
                      <span>Email:</span>
                      {student.email}
                    </Typography>
                    <Typography gutterBottom variant="h5" component="div">
                      <span>Class:</span>
                      {student.student_class.class_text}
                    </Typography>
                    <Typography gutterBottom variant="h5" component="div">
                      <span>Age:</span>
                      {student.age}
                    </Typography>
                    <Typography gutterBottom variant="h5" component="div">
                      <span>Gender:</span>
                      {student.gender}
                    </Typography>
                    <Typography gutterBottom variant="h5" component="div">
                      <span>Guardian Name:</span> {student.guardian}
                    </Typography>
                    <Typography gutterBottom variant="h5" component="div">
                      <span>Guardian Phone:</span> {student.guardian_phone}
                    </Typography>

                    <Typography
                      variant="body2"
                      sx={{ color: "text.secondary" }}
                    >
                      Lizards are a widespread group of squamate reptiles, with
                      over 6,000 species, ranging across all continents except
                      Antarctica
                    </Typography>
                  </CardContent>
                  {/* {user?.role === "COACHING" && student?.qr_id && (
                    <div>
                      <QRCode value={student.qr_id} size={150} />

                      <Button
                        onClick={() => {
                          downloadQR(student._id);
                        }}
                      >
                        Download QR PDF
                      </Button>
                    </div>
                  )} */}
                  {user?.role === "COACHING" && student?.qr_id && (
                    <div ref={(el) => (qrRefs.current[student._id] = el)}>
                      <QRCode value={student.qr_id} size={150} />

                      <Button
                        onClick={() => {
                          downloadQR(student._id);
                        }}
                      >
                        Download QR PDF
                      </Button>
                    </div>
                  )}

                  <Button
                    onClick={() => {
                      handleEdit(student._id);
                    }}
                  >
                    <EditIcon />
                  </Button>
                  <Button
                    onClick={() => {
                      handleDelete(student._id);
                    }}
                    sx={{ marginLeft: "18px" }}
                  >
                    <DeleteIcon sx={{ color: "red" }} />
                  </Button>
                </CardActionArea>
              </Card>
            );
          })}
      </Box>
    </Box>
  );
}
