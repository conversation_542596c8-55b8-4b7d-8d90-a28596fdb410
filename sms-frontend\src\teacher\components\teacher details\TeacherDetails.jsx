import * as React from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";

import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { server } from "../../../server";
import axios from "axios";
import { Avatar, Box } from "@mui/material";

export default function TeacherDetails() {
  const [teacherDetails, setTeacherDetails] = React.useState(null);
  const fetchTeacherDetails = async () => {
    try {
      const response = await axios.get(`${server}/api/teacher/fetch-single`);
      console.log("teacher details", response.data.teacher);
      setTeacherDetails(response.data.teacher);
    } catch (error) {
      console.log("Error in teacher details fetching single data.", error);
    }
  };

  React.useEffect(() => {
    fetchTeacherDetails();
  }, []);

  return (
    <>
      {teacherDetails && (
        <>
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              marginBottom: "10px",
              padding: "25px",
            }}
          >
            <Avatar
              alt="Remy Sharp"
              src={`./images/uploaded/teacher/${teacherDetails.teacher_image}`}
              sx={{ width: 356, height: 356 }}
            />
          </Box>
          <TableContainer component={Paper}>
            <Table sx={{ minWidth: 650 }} aria-label="simple table">
              <TableBody>
                <TableRow>
                  <TableCell>
                    <b>Name:</b>
                  </TableCell>
                  <TableCell align="right">{teacherDetails.name}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>
                    <b>Email:</b>
                  </TableCell>
                  <TableCell align="right">{teacherDetails.email}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>
                    <b>Age:</b>
                  </TableCell>
                  <TableCell align="right">{teacherDetails.age}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>
                    <b>Gender:</b>
                  </TableCell>
                  <TableCell align="right">{teacherDetails.gender}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>
                    <b>Qualification:</b>
                  </TableCell>
                  <TableCell align="right">
                    {teacherDetails.qualification}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </>
      )}
    </>
  );
}
