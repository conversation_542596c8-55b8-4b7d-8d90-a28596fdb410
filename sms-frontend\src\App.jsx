import { useState } from "react";
import { BrowserRouter, Router, Route, Routes } from "react-router-dom";
import School from "./school/School";
import Class from "./school/components/class/Class";
import Dashboard from "./school/components/dashboard/Dashboard";
import Examinations from "./school/components/examinations/Examinations";
import Notice from "./school/components/notice/Notice";
import Scheduel from "./school/components/schedule/Scheduel";
import Students from "./school/components/students/Students";
import Subjects from "./school/components/subjects/Subjects";
import Teachers from "./school/components/teachers/Teachers";
import Client from "./client/Client";
import Home from "./client/components/home/<USER>";
import Login from "./client/components/login/Login";
import Register from "./client/components/register/Register";
import Teacher from "./teacher/Teacher";
import TeacherDetails from "./teacher/components/teacher details/TeacherDetails";
import TeacherAttendance from "./teacher/components/attendance/TeacherAttendance";
import TeacherExaminations from "./teacher/components/examinations/TeacherExaminations";
import TeacherNotice from "./teacher/components/notice/TeacherNotice";
import TeacherScheduel from "./teacher/components/scheduel/TeacherScheduel";
import Student from "./student/Student";
import StudentAttendance from "./student/components/attendance/StudentAttendance";
import StudentExaminations from "./student/components/examinations/StudentExaminations";
import StudentNotice from "./student/components/notice/StudentNotice";
import StudentScheduel from "./student/components/scheduel/StudentScheduel";
import StudentDetails from "./student/components/student details/StudentDetails";
import ProtectedRoute from "./guard/ProtectedRoute";
import { AuthProvider } from "./context/AuthContext";
import { ChatProvider } from "./context/ChatContext";
import Logout from "./client/components/logout/Logout";
import FileManagement from "./school/components/filemanagemnt/FileManagemnt";
import AttendanceStudentList from "./school/components/attendance/AttendanceStudentList";
import AttendanceDetails from "./school/components/attendance/AttendaceDetails";
import QrAttendanceStudentList from "./school/components/QR-attendance/QrAttendanceStudentList";
import QrAttendanceDetails from "./school/components/QR-attendance/QrAttendaceDetails";
import QrScanner from "./school/components/QR-attendance/QrScanner";
import SuperAdmin from "./superadmin/SuperAdmin";
import SuperAdminDashboard from "./superadmin/components/dashboard/SuperAdminDashboard";
import SuperAdminLogin from "./client/components/login/SuperAdminLogin";
import SchoolRegistration from "./superadmin/components/SchoolRegistration/SchoolRegistration";
import Chat from "./school/components/chat/Chat";
import TeacherChat from "./teacher/components/chat/TeacherChat";
import StudentChat from "./student/components/chat/StudentChat";

function App() {
  return (
    <AuthProvider>
      <ChatProvider>
        <BrowserRouter>
          <Routes>
            {/* super admin */}
            <Route
              path="superadmin"
              element={
                <ProtectedRoute allowedRoles={["SUPERADMIN"]}>
                  <SuperAdmin />
                </ProtectedRoute>
              }
            >
              <Route index element={<SuperAdminDashboard />} />
              <Route path="register" element={<SchoolRegistration />} />
            </Route>
            {/* school */}
            <Route
              path="school"
              element={
                <ProtectedRoute allowedRoles={["SCHOOL", "COACHING"]}>
                  <School />
                </ProtectedRoute>
              }
            >
              <Route index element={<Dashboard />} />
              <Route path="dashboard" element={<Dashboard />} />
              <Route path="chat" element={<Chat />} />
              <Route
                path="QR-base-attendance"
                element={
                  <ProtectedRoute allowedRoles={["COACHING"]}>
                    <QrAttendanceStudentList />
                  </ProtectedRoute>
                }
              />
              <Route
                path="QR-base-attendance/:id"
                element={
                  <ProtectedRoute allowedRoles={["COACHING"]}>
                    <QrAttendanceDetails />
                  </ProtectedRoute>
                }
              />
              <Route
                path="QR-Scan"
                element={
                  <ProtectedRoute allowedRoles={["COACHING"]}>
                    <QrScanner />
                  </ProtectedRoute>
                }
              />
              <Route
                path="attendance"
                element={
                  <ProtectedRoute allowedRoles={["SCHOOL"]}>
                    <AttendanceStudentList />
                  </ProtectedRoute>
                }
              />
              <Route
                path="attendance/:id"
                element={
                  <ProtectedRoute allowedRoles={["SCHOOL"]}>
                    <AttendanceDetails />
                  </ProtectedRoute>
                }
              />

              <Route path="class" element={<Class />} />
              <Route path="examinations" element={<Examinations />} />
              <Route path="notice" element={<Notice />} />
              <Route path="file-upload" element={<FileManagement />} />
              <Route path="scheduel" element={<Scheduel />} />
              <Route path="students" element={<Students />} />
              <Route path="subjects" element={<Subjects />} />
              <Route path="teachers" element={<Teachers />} />
            </Route>

            {/* student */}
            <Route
              path="student"
              element={
                <ProtectedRoute allowedRoles={["STUDENT"]}>
                  <Student />
                </ProtectedRoute>
              }
            >
              <Route index element={<StudentDetails />} />
              <Route path="attendance" element={<StudentAttendance />} />
              <Route path="examinations" element={<StudentExaminations />} />
              <Route path="notice" element={<StudentNotice />} />
              <Route path="scheduel" element={<StudentScheduel />} />
              <Route path="chat" element={<StudentChat />} />
            </Route>
            {/* teacher */}
            <Route
              path="teacher"
              element={
                <ProtectedRoute allowedRoles={["TEACHER", "TEACHER1"]}>
                  <Teacher />
                </ProtectedRoute>
              }
            >
              <Route index element={<TeacherDetails />} />
              <Route
                path="attendance"
                element={
                  <ProtectedRoute allowedRoles={["TEACHER"]}>
                    <TeacherAttendance />
                  </ProtectedRoute>
                }
              />
              <Route path="examinations" element={<TeacherExaminations />} />
              <Route path="notice" element={<TeacherNotice />} />
              <Route path="scheduel" element={<TeacherScheduel />} />
              <Route path="chat" element={<TeacherChat />} />
            </Route>
            {/* public or client */}
            <Route path="" element={<Client />}>
              <Route index element={<Home />} />
              <Route path="login" element={<Login />} />
              <Route path="logout" element={<Logout />} />
              <Route path="super-admin-login" element={<SuperAdminLogin />} />
            </Route>
          </Routes>
        </BrowserRouter>
      </ChatProvider>
    </AuthProvider>
  );
}

export default App;
