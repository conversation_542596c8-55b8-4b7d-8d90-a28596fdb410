import axios from "axios";
import { useEffect, useState } from "react";
import { server } from "../../../server";
import {
  Paper,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { PieChart } from "@mui/x-charts/PieChart";
import { styled } from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";

const Item = styled(Paper)(({ theme }) => ({
  backgroundColor: "#fff",
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: "center",
  color: (theme.vars ?? theme).palette.text.secondary,
  ...theme.applyStyles("dark", {
    backgroundColor: "#1A2027",
  }),
}));

export default function StudentAttendance() {
  const [present, setPresent] = useState(0);
  const [absent, setAbsent] = useState(0);
  const [attendanceData, setAttendanceData] = useState([]);
  const [studentInfo, setStudentInfo] = useState(null);
  const [selectedDate, setSelectedDate] = useState(dayjs());
  const [user, setUser] = useState(null);

  const fetchAttendanceData = async (date) => {
    try {
      console.log("todays date", date);
      const month = date.month() + 1; // dayjs month is 0-
      console.log("month", month);
      const year = date.year();
      console.log("year", year);
      console.log("user", user);
      if (user?.belongs_to === "SCHOOL") {
        var link = "attendance";
      } else {
        var link = "Qr-code-attendance";
      }
      console.log("link", link);
      const response = await axios.get(
        `${server}/api/${link}/getstudentattendance`,
        {
          params: {
            month,
            year,
          },
        }
      );

      const respData = response.data;
      console.log("attendance data", respData);
      setAttendanceData(respData);

      let presentCount = 0;
      let absentCount = 0;

      respData.forEach((attendance) => {
        if (attendance.status.toLowerCase() === "present") presentCount++;
        else if (attendance.status.toLowerCase() === "absent") absentCount++;
      });

      setPresent(presentCount);
      setAbsent(absentCount);

      if (respData.length > 0) {
        setStudentInfo(respData[0].student);
      }
    } catch (error) {
      console.log("Error in fetching student attendance:", error);
    }
  };

  useEffect(() => {
    const userStr = localStorage.getItem("user");
    if (userStr) {
      setUser(JSON.parse(userStr));
    }
  }, []);

  useEffect(() => {
    fetchAttendanceData(selectedDate);
  }, [selectedDate]);

  const convertDate = (dateStr) => {
    return dayjs(dateStr).format("ddd- D MMM,YYYY"); // Output: 1 June, 2025
  };
  return (
    <>
      <h1>Attendance Details</h1>

      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker
          views={["year", "month"]}
          label="Select Month and Year"
          value={selectedDate}
          onChange={(newValue) => setSelectedDate(newValue)}
          sx={{ mb: 2 }}
        />
      </LocalizationProvider>

      {studentInfo && (
        <Paper sx={{ p: 2, mb: 2 }}>
          <h2>Student Info</h2>
          <p>
            <strong>Name:</strong> {studentInfo.name}
          </p>
          <p>
            <strong>Age:</strong> {studentInfo.age}
          </p>
          <p>
            <strong>Email:</strong> {studentInfo.email}
          </p>
          <p>
            <strong>Guardian:</strong> {studentInfo.guardian}
          </p>
          <p>
            <strong>Phone:</strong> {studentInfo.guardian_phone}
          </p>
        </Paper>
      )}

      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <Item>
            <PieChart
              series={[
                {
                  data: [
                    { id: 0, value: present, label: `Present (${present})` },
                    { id: 1, value: absent, label: `Absent (${absent})` },
                  ],
                },
              ]}
              width={200}
              height={200}
            />
          </Item>
        </Grid>

        <Grid item xs={12} md={6}>
          <Item>
            <TableContainer component={Paper}>
              <Table sx={{ minWidth: 450 }} aria-label="simple table">
                <TableHead>
                  <TableRow>
                    <TableCell>Date</TableCell>
                    <TableCell align="right">Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {attendanceData.map((attendance) => (
                    <TableRow key={attendance._id}>
                      <TableCell>{convertDate(attendance.date)}</TableCell>
                      <TableCell align="right">{attendance.status}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Item>
        </Grid>
      </Grid>
    </>
  );
}
