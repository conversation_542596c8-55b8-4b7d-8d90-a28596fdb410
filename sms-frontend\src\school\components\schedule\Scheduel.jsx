import React, { useState } from "react";
import {
  Dnd<PERSON>ontext,
  useSensor,
  useSensors,
  PointerSensor,
  DragOverlay,
  useDroppable,
} from "@dnd-kit/core";
import { restrictToWindowEdges } from "@dnd-kit/modifiers";
import { CSS } from "@dnd-kit/utilities";
import { useSortable } from "@dnd-kit/sortable";

import { useFormik } from "formik";
import { useEffect } from "react";
import axios from "axios";
import { server } from "../../../server";
import { periodSchema } from "../../../yupSchema/periodSchema";
import MessageSnackbar from "../../../basic utility component/snakebar/MessageSnakebar";
import { Calendar, momentLocalizer } from "react-big-calendar";
import moment from "moment";
import "react-big-calendar/lib/css/react-big-calendar.css";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
} from "@mui/material";

import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";

const localizer = momentLocalizer(moment);

// Generic Item component for drag and drop
const DraggableItem = ({ id, text, subtitle, isActive }) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    padding: "8px 12px",
    margin: "4px",
    border: "1px solid #ccc",
    borderRadius: "4px",
    backgroundColor: isActive ? "#e0e0e0" : "#f5f5f5",
    cursor: "grab",
    userSelect: "none",
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <div>{text}</div>
      {subtitle && (
        <div style={{ fontSize: "0.8rem", color: "#666" }}>{subtitle}</div>
      )}
    </div>
  );
};

// Generic drop zone component
const DropZone = ({ id, selectedItem, itemText, placeholder }) => {
  const { setNodeRef, isOver } = useDroppable({
    id,
  });

  return (
    <div
      ref={setNodeRef}
      style={{
        border: `2px dashed ${isOver ? "#4caf50" : "#1976d2"}`,
        borderRadius: "8px",
        padding: "16px",
        minHeight: "60px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: isOver
          ? "rgba(76, 175, 80, 0.1)"
          : selectedItem
          ? "#e3f2fd"
          : "#f5f5f5",
        marginBottom: "16px",
        transition: "all 0.2s ease",
      }}
    >
      {selectedItem ? (
        <div
          style={{
            padding: "8px 12px",
            borderRadius: "4px",
            backgroundColor: "#1976d2",
            color: "white",
          }}
        >
          {itemText}
        </div>
      ) : (
        <Typography color="textSecondary">
          {isOver ? "Drop to select" : placeholder}
        </Typography>
      )}
    </div>
  );
};

// Generic selector with drag and drop
const DragDropSelector = ({
  title,
  items,
  value,
  onChange,
  fieldName,
  getItemText,
  getItemSubtitle,
  dropzoneId,
}) => {
  const [activeId, setActiveId] = useState(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    })
  );

  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = (event) => {
    const { over, active } = event;
    setActiveId(null);

    if (over && over.id === dropzoneId) {
      // Item was dropped in the dropzone
      onChange({
        target: {
          name: fieldName,
          value: active.id,
        },
      });
    } else if (!over && value === active.id) {
      // Item was dragged out and it was the selected one
      onChange({
        target: {
          name: fieldName,
          value: "",
        },
      });
    }
  };

  const handleDragCancel = () => {
    setActiveId(null);
  };

  // Filter out the selected item from available options
  const availableItems = items.filter((item) =>
    typeof item === "object" ? item._id !== value : item !== value
  );

  const selectedItem = items.find((item) =>
    typeof item === "object" ? item._id === value : item === value
  );

  const selectedItemText = selectedItem ? getItemText(selectedItem) : "";

  return (
    <div>
      <Typography variant="subtitle1" sx={{ mb: 1 }}>
        {title}
      </Typography>
      <DndContext
        sensors={sensors}
        modifiers={[restrictToWindowEdges]}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragCancel={handleDragCancel}
      >
        <DropZone
          id={dropzoneId}
          selectedItem={value}
          itemText={selectedItemText}
          placeholder={`Drag a ${title.toLowerCase()} here`}
        />

        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          Options{" "}
          {availableItems.length < items.length
            ? `(${availableItems.length} of ${items.length})`
            : ""}
        </Typography>
        <div style={{ display: "flex", flexWrap: "wrap", maxWidth: "100%" }}>
          {availableItems.map((item) => (
            <DraggableItem
              key={typeof item === "object" ? item._id : item}
              id={typeof item === "object" ? item._id : item}
              text={getItemText(item)}
              subtitle={getItemSubtitle ? getItemSubtitle(item) : null}
              isActive={
                activeId === (typeof item === "object" ? item._id : item)
              }
            />
          ))}
        </div>

        <DragOverlay>
          {activeId
            ? (() => {
                const activeItem = items.find((item) =>
                  typeof item === "object"
                    ? item._id === activeId
                    : item === activeId
                );
                if (activeItem) {
                  return (
                    <div
                      style={{
                        padding: "8px 12px",
                        borderRadius: "4px",
                        backgroundColor: "#bbdefb",
                        boxShadow: "0 5px 10px rgba(0,0,0,0.2)",
                      }}
                    >
                      {getItemText(activeItem)}
                      {getItemSubtitle && (
                        <div style={{ fontSize: "0.8rem", color: "#666" }}>
                          {getItemSubtitle(activeItem)}
                        </div>
                      )}
                    </div>
                  );
                }
                return null;
              })()
            : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
};

export default function Scheduel() {
  const [newperiod, setNewPeriod] = useState(false);
  const [view, setView] = useState("week"); // Add this line
  const date = new Date();
  const [classes, setClasses] = useState([]);
  const [subjects, setSubjects] = useState([]);
  const [teachers, setTeachers] = useState([]);
  const [message, setMessage] = useState("");
  const [messageType, setMessageType] = useState("success");
  const [params, setParams] = useState({});
  const [events, setEvents] = useState([]);
  const [scheduleData, setScheduleData] = useState([]);
  const handleClass = (e) => {
    const value = e.target.value || undefined;
    setParams((prev) => ({ ...prev, Class: value }));
  };
  const handleMessageClose = () => {
    setMessage("");
  };
  const days = [
    "Sunday",
    "Monday",
    "Tuesday",
    "wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];
  const period = [
    "first",
    "Second",
    "Third",
    "Fourth",
    "Fifth",
    "Sixth",
    "Seventh",
    "Eight",
    "Ninth",
    "Tenth",
  ];

  const initialValues = {
    teacher: "",
    subject: "",
    class: "",
    days: "",
    period: "",
    start_time: "",
    end_time: "",
  };
  const Formik = useFormik({
    initialValues,
    validationSchema: periodSchema,
    onSubmit: (values) => {
      console.log("Register submit values", values);
      axios
        .post(`${server}/api/schedule/create`, { ...values })
        .then((response) => {
          setMessage(response.data.message);
          setMessageType("Success");
        })
        .catch((e) => {
          console.log("Error in Adding subject", e);
          setMessage("error in saving Subject");
          setMessageType("error");
        });

      Formik.resetForm();
    },
  });

  useEffect(() => {
    console.log(params);
    const fetchSchedule = () => {
      axios
        .get(`${server}/api/schedule/fetch-with-query`, { params })
        .then((response) => {
          console.log("schedule_data", response.data.schedules);
          setScheduleData(response.data.schedules);
        })
        .catch((e) => {
          console.log("Error in fetching Students.");
        });
    };

    fetchSchedule();
  }, [message, params]);

  const fetchClasses = () => {
    axios
      .get(`${server}/api/class/all`)
      .then((response) => {
        setClasses(response.data.data);
      })
      .catch((e) => {
        console.log("Error in fetching class.");
      });
  };
  const fetchAllSubjects = () => {
    axios
      .get(`${server}/api/subject/all`)
      .then((response) => {
        setSubjects(response.data.data);
      })
      .catch((e) => {
        console.log("Error in fetching all subjects", e);
      });
  };
  const fetchTeachers = () => {
    axios
      .get(`${server}/api/teacher/fetch-with-query`)
      .then((response) => {
        setTeachers(response.data.teachers);
      })
      .catch((e) => {
        console.log("Error in fetching Teachers.");
      });
  };
  useEffect(() => {
    fetchClasses();
    fetchAllSubjects();
    fetchTeachers();
  }, []);

  const getScheduleFor = (day, period) => {
    return scheduleData.filter(
      (item) =>
        item.days === day && item.period.toLowerCase() === period.toLowerCase()
    );
  };
  const handleDelete = (id) => {
    if (confirm("Are you sure want to delete")) {
      console.log("Clicked record ID:", id);
      axios
        .delete(`${server}/api/schedule/delete/${id}`)
        .then((response) => {
          setMessage(response.data.message);
          setMessageType("Success");
        })
        .catch((e) => {
          console.log("Error in Adding subject", e);
          setMessage("error in saving Subject");
          setMessageType("error");
        });
    }
    // Add your logic here (e.g., open modal, navigate, etc.)
  };

  return (
    <>
      <h1>Scheduel</h1>

      <Box
        component={"div"}
        sx={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "center",
          marginTop: "30px",
        }}
      >
        <FormControl sx={{ width: "230px", marginLeft: "10px" }}>
          <InputLabel id="demo-simple-select-label">Student Class</InputLabel>
          <Select
            label="Student Class"
            onChange={(e) => {
              handleClass(e);
            }}
          >
            <MenuItem value=""> Un-Select</MenuItem>
            {classes &&
              classes.map((x) => {
                return (
                  <MenuItem key={x._id} value={x._id}>
                    {x.class_text} ({x.class_num})
                  </MenuItem>
                );
              })}
          </Select>
        </FormControl>
      </Box>

      {newperiod && (
        <>
          {message && (
            <MessageSnackbar
              message={message}
              type={messageType}
              handleClose={handleMessageClose}
            />
          )}
          <h1>scheduel event</h1>
          <Box
            component="form"
            sx={{
              "& > :not(style)": { m: 1 },
              display: "flex",
              flexDirection: "column",
              width: "60vw",
              minWidth: "230px",
              margin: "auto",
            }}
            noValidate
            autoComplete="off"
            onSubmit={Formik.handleSubmit}
          >
            {/* Teacher selector */}
            <DragDropSelector
              title="Teacher"
              items={teachers}
              value={Formik.values.teacher}
              onChange={Formik.handleChange}
              fieldName="teacher"
              getItemText={(item) => `${item.name}`}
              getItemSubtitle={(item) => `${item.qualification}`}
              dropzoneId="teacher-dropzone"
            />
            {Formik.touched.teacher && Formik.errors.teacher && (
              <p style={{ color: "red", textTransform: "capitalize" }}>
                {Formik.errors.teacher}
              </p>
            )}

            {/* Class selector */}
            <DragDropSelector
              title="Class"
              items={classes}
              value={Formik.values.class}
              onChange={Formik.handleChange}
              fieldName="class"
              getItemText={(item) => `${item.class_text} (${item.class_num})`}
              dropzoneId="class-dropzone"
            />
            {Formik.touched.class && Formik.errors.class && (
              <p style={{ color: "red", textTransform: "capitalize" }}>
                {Formik.errors.class}
              </p>
            )}

            {/* Subject selector */}
            <DragDropSelector
              title="Subject"
              items={subjects}
              value={Formik.values.subject}
              onChange={Formik.handleChange}
              fieldName="subject"
              getItemText={(item) => `${item.subject_name}`}
              getItemSubtitle={(item) => `${item.subject_codename}`}
              dropzoneId="subject-dropzone"
            />
            {Formik.touched.subject && Formik.errors.subject && (
              <p style={{ color: "red", textTransform: "capitalize" }}>
                {Formik.errors.subject}
              </p>
            )}

            {/* Period selector */}
            <DragDropSelector
              title="Period"
              items={period}
              value={Formik.values.period}
              onChange={Formik.handleChange}
              fieldName="period"
              getItemText={(item) => item}
              dropzoneId="period-dropzone"
            />
            {Formik.touched.period && Formik.errors.period && (
              <p style={{ color: "red", textTransform: "capitalize" }}>
                {Formik.errors.period}
              </p>
            )}

            {/* Day selector */}
            <DragDropSelector
              title="Day"
              items={days}
              value={Formik.values.days}
              onChange={Formik.handleChange}
              fieldName="days"
              getItemText={(item) => item}
              dropzoneId="day-dropzone"
            />
            {Formik.touched.days && Formik.errors.days && (
              <p style={{ color: "red", textTransform: "capitalize" }}>
                {Formik.errors.days}
              </p>
            )}
            {/* start time  */}
            <TextField
              name="start_time"
              label="Start Time"
              value={Formik.values.start_time}
              onChange={Formik.handleChange}
              onBlur={Formik.handleBlur}
            />
            {Formik.touched.start_time && Formik.errors.start_time && (
              <p style={{ color: "red", textTransform: "capitalize" }}>
                {Formik.errors.start_time}
              </p>
            )}
            {/* end time */}
            <TextField
              name="end_time"
              label="End Time"
              value={Formik.values.end_time}
              onChange={Formik.handleChange}
              onBlur={Formik.handleBlur}
            />
            {Formik.touched.end_time && Formik.errors.end_time && (
              <p style={{ color: "red", textTransform: "capitalize" }}>
                {Formik.errors.end_time}
              </p>
            )}
            <Button type="submit" variant="contained">
              Add
            </Button>
          </Box>
        </>
      )}

      {params.Class ? (
        <>
          <Button onClick={() => setNewPeriod(true)}>Add Period</Button>

          {newperiod && (
            <Button onClick={() => setNewPeriod(false)}>Chancel</Button>
          )}
          <Box sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom align="center">
              Weekly Class Schedule
            </Typography>

            <TableContainer component={Paper} sx={{ overflowX: "auto" }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <strong>Day</strong>
                    </TableCell>
                    {period.map((p) => (
                      <TableCell key={p} align="center">
                        <strong>
                          {p.charAt(0).toUpperCase() + p.slice(1)} Period
                        </strong>
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>

                <TableBody>
                  {days.map((day) => (
                    <TableRow key={day}>
                      <TableCell>{day}</TableCell>
                      {period.map((period) => {
                        const entries = getScheduleFor(day, period);
                        return (
                          <TableCell key={period} align="center">
                            {entries.length > 0 ? (
                              entries.map((entry, idx) => (
                                <Box
                                  key={idx}
                                  mb={1}
                                  p={1}
                                  sx={{
                                    border: "1px solid #eee",
                                    borderRadius: 1,
                                    cursor: "pointer",
                                    transition: "0.2s",
                                    "&:hover": { backgroundColor: "#f5f5f5" },
                                  }}
                                  onClick={() => handleDelete(entry._id)}
                                >
                                  <Typography
                                    variant="subtitle2"
                                    sx={{ fontWeight: "bold" }}
                                  >
                                    {entry.subject.subject_codename}
                                  </Typography>
                                  <Typography variant="body2">
                                    {entry.startTime} - {entry.endTime}
                                  </Typography>
                                  <Typography variant="body2">
                                    {entry.class.class_text} -
                                    {entry.class.class_num}
                                  </Typography>
                                  <Typography
                                    variant="body2"
                                    color="textSecondary"
                                  >
                                    {entry.teacher.name}
                                  </Typography>
                                </Box>
                              ))
                            ) : (
                              <Typography variant="body2" color="textSecondary">
                                —
                              </Typography>
                            )}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </>
      ) : (
        <Typography>Select Class to Show Scheduel</Typography>
      )}
    </>
  );
}
