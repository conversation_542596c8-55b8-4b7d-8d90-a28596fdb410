import { createContext, useState, useEffect, useContext } from "react";
import { io } from "socket.io-client";
import { server } from "../server";
import { AuthContext } from "./AuthContext";
import axios from "axios";

export const ChatContext = createContext();

export const ChatProvider = ({ children }) => {
  const { user } = useContext(AuthContext);
  const [socket, setSocket] = useState(null);
  const [chatGroups, setChatGroups] = useState([]);
  const [currentGroup, setCurrentGroup] = useState(null);
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [unreadCounts, setUnreadCounts] = useState({});
  const [socketConnected, setSocketConnected] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  // Revert to the working solution for real-time messaging
  useEffect(() => {
    if (user) {
      console.log(
        "Initializing socket connection with user:",
        user.id,
        user
        // user.teacher_name
      );

      // Create socket with explicit transports to avoid polling issues
      const newSocket = io(server, {
        transports: ["websocket", "polling"],
        reconnection: true,
        reconnectionAttempts: 10,
        reconnectionDelay: 1000,
        timeout: 20000,
      });

      newSocket.on("connect", () => {
        console.log("Socket connected:", newSocket.id);
        setSocketConnected(true);

        // Register user with socket
        newSocket.emit("register_user", {
          userId: user.id,
          role: user.role,
        });

        // Rejoin current room if there was one
        if (currentGroup) {
          console.log("Rejoining room after connect:", currentGroup);
          newSocket.emit("join_room", currentGroup, (response) => {
            if (response && response.success) {
              console.log("Successfully rejoined room:", currentGroup);
            } else {
              console.error("Failed to rejoin room:", currentGroup);
            }
          });
        }
      });

      newSocket.on("disconnect", () => {
        console.log("Socket disconnected");
        setSocketConnected(false);
      });

      newSocket.on("connect_error", (error) => {
        console.error("Socket connection error:", error);
        setSocketConnected(false);
      });

      setSocket(newSocket);

      return () => {
        console.log("Disconnecting socket");
        newSocket.disconnect();
      };
    }
  }, [user]);

  // Fetch chat groups
  const fetchChatGroups = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${server}/api/chat/groups`);
      setChatGroups(response.data.data);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching chat groups:", error);
      setLoading(false);
    }
  };

  // Fetch messages for a group
  const fetchMessages = async (groupId, pageNum = 1, append = false) => {
    try {
      if (pageNum === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const response = await axios.get(
        `${server}/api/chat/messages/${groupId}?page=${pageNum}&limit=20`
      );

      // Process messages with sender info
      const messagesWithSenderInfo = response.data.data.map((msg) => {
        if (msg.sender === user.id) {
          return {
            ...msg,
            senderInfo: {
              ...msg.senderInfo,
              name: user.name || "User",
              id: user.id,
            },
          };
        }
        return {
          ...msg,
          senderInfo: msg.senderInfo || { name: "Unknown User" },
        };
      });

      // Update messages state based on whether we're appending or replacing
      if (append) {
        setMessages((prev) => [...messagesWithSenderInfo, ...prev]);
      } else {
        setMessages(messagesWithSenderInfo);
      }

      // Update pagination state
      setHasMore(messagesWithSenderInfo.length === 20);
      setPage(pageNum);

      // Reset unread count for this group
      console.log(
        "Resetting unread count for group in fetchMessages:",
        groupId
      );
      setUnreadCounts((prev) => {
        const newCounts = { ...prev, [groupId]: 0 };
        console.log(
          "New unread counts after reset in fetchMessages:",
          newCounts
        );
        return newCounts;
      });

      if (pageNum === 1) {
        setLoading(false);
      } else {
        setLoadingMore(false);
      }
    } catch (error) {
      console.error("Error fetching messages:", error);
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // Improved join chat room function
  const joinChatRoom = (groupId) => {
    if (!socket || !socketConnected) {
      console.error("Cannot join room: Socket not connected");
      return;
    }

    console.log("Joining room:", groupId);

    socket.emit("join_room", groupId, (response) => {
      if (response && response.success) {
        console.log("Successfully joined room:", groupId);
      } else {
        console.error("Failed to join room:", groupId, response?.error);
      }
    });

    // Reset unread count for this group
    console.log("Resetting unread count for group:", groupId);
    setUnreadCounts((prev) => {
      const newCounts = { ...prev, [groupId]: 0 };
      console.log("New unread counts after reset:", newCounts);
      return newCounts;
    });

    setCurrentGroup(groupId);
    setPage(1);
    setHasMore(true);

    fetchMessages(groupId, 1, false);
  };

  // Add a function to load more messages
  const loadMoreMessages = async () => {
    if (!currentGroup) return;

    try {
      // Get the current page from state
      const nextPage = page + 1;

      // Fetch messages for the next page
      const response = await axios.get(
        `${server}/api/chat/messages/${currentGroup}?page=${nextPage}&limit=20`
      );

      // Process messages with sender info
      const messagesWithSenderInfo = response.data.data.map((msg) => {
        if (msg.sender === user.id) {
          return {
            ...msg,
            senderInfo: {
              ...msg.senderInfo,
              name: user.name || "User",
              id: user.id,
            },
          };
        }
        return {
          ...msg,
          senderInfo: msg.senderInfo || { name: "Unknown User" },
        };
      });

      // Update messages state by prepending the new messages
      setMessages((prev) => [...messagesWithSenderInfo, ...prev]);

      // Update pagination state
      setHasMore(messagesWithSenderInfo.length === 20);
      setPage(nextPage);
    } catch (error) {
      console.error("Error loading more messages:", error);
    }
  };

  // Update the sendMessage function to include proper user name
  const sendMessage = (content) => {
    if (!socket) {
      console.error("Cannot send message: Socket not connected");
      return;
    }

    if (!socketConnected) {
      console.error("Cannot send message: Socket not connected");
      return;
    }

    if (!currentGroup) {
      console.error("Cannot send message: No group selected");
      return;
    }

    if (!user) {
      console.error("Cannot send message: User not authenticated");
      return;
    }

    console.log("Sending message to group:", currentGroup);

    const tempId = Date.now().toString();
    const messageData = {
      groupId: currentGroup,
      senderId: user.id,
      senderType:
        user.role === "SCHOOL" || user.role === "COACHING"
          ? "School"
          : user.role === "TEACHER" || user.role === "TEACHER1"
          ? "Teacher"
          : "Student",
      content,
      senderInfo: {
        name:
          user.name ||
          (user.teacher_name && `${user.teacher_name} (teacher)`) ||
          (user.student_name && `${user.student_name} (student)`) ||
          "User",
        id: user.id,
      },
      tempId,
    };

    // Add callback to confirm message was received by server
    socket.emit("send_message", messageData, (ack) => {
      if (ack && ack.success) {
        console.log("Server acknowledged message:", tempId);
      } else {
        console.error("Server failed to acknowledge message:", tempId);
      }
    });

    // Optimistically add message to UI
    setMessages((prev) => [
      ...prev,
      {
        ...messageData,
        timestamp: new Date(),
      },
    ]);
  };

  // Improved message listener with better logging
  useEffect(() => {
    if (socket) {
      console.log(
        "Setting up receive_message listener for group:",
        currentGroup
      );

      // Remove any existing listeners to prevent duplicates
      socket.off("receive_message");

      // Add the listener
      socket.on("receive_message", (message) => {
        console.log("Received message from server:", message);

        // Check if message is from current user
        const isSentByMe = message.senderId === user.id;

        // Only increment unread count if:
        // 1. Message is not from current user
        // 2. Message is for a different group than the current one
        if (!isSentByMe && message.groupId !== currentGroup) {
          console.log("Incrementing unread count for group:", message.groupId);

          setUnreadCounts((prev) => {
            const newCounts = {
              ...prev,
              [message.groupId]: (prev[message.groupId] || 0) + 1,
            };
            console.log("New unread counts:", newCounts);
            return newCounts;
          });
        }

        // Process the message for display
        if (message.groupId === currentGroup) {
          console.log("Message is for current group:", currentGroup);

          setMessages((prev) => {
            // Check if we already have this message (by _id or tempId)
            const messageExists = prev.some(
              (msg) =>
                (msg._id && msg._id === message._id) ||
                (msg.tempId && msg.tempId === message.tempId)
            );

            if (messageExists) {
              // Update the existing message
              return prev.map((msg) => {
                if (
                  (msg._id && msg._id === message._id) ||
                  (msg.tempId && msg.tempId === message.tempId)
                ) {
                  return {
                    ...message,
                    senderInfo: message.senderInfo || {
                      name: isSentByMe
                        ? user.name ||
                          (user.teacher_name &&
                            `${user.teacher_name} (teacher)`) ||
                          (user.student_name &&
                            `${user.student_name} (student)`) ||
                          "User"
                        : "Unknown User",
                    },
                  };
                }
                return msg;
              });
            } else {
              // Add the new message
              return [
                ...prev,
                {
                  ...message,
                  senderInfo: message.senderInfo || {
                    name: isSentByMe
                      ? user.name ||
                        (user.teacher_name &&
                          `${user.teacher_name} (teacher)`) ||
                        (user.student_name &&
                          `${user.student_name} (student)`) ||
                        "User"
                      : "Unknown User",
                  },
                },
              ];
            }
          });
        }
      });
    }

    return () => {
      if (socket) {
        console.log("Removing receive_message listener");
        socket.off("receive_message");
      }
    };
  }, [socket, currentGroup, user]);

  // Create a new chat group
  const createChatGroup = async (groupData) => {
    try {
      setLoading(true);
      const response = await axios.post(
        `${server}/api/chat/group/create`,
        groupData
      );

      if (response.data.success) {
        await fetchChatGroups();
      }

      setLoading(false);
      return response.data;
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  // Update a chat group (add/remove members, change admin status)
  const updateChatGroup = async (groupId, updateData) => {
    try {
      setLoading(true);
      const response = await axios.patch(
        `${server}/api/chat/group/${groupId}`,
        updateData
      );

      if (response.data.success) {
        await fetchChatGroups();

        if (currentGroup === groupId) {
          const currentGroupData = response.data.data;
          // Check if current user is still in the group
          const userType =
            user.role === "TEACHER" || user.role === "TEACHER1"
              ? "Teacher"
              : "Student";
          const stillInGroup = currentGroupData.members.some(
            (m) => m.user === user.id && m.userType === userType
          );

          if (!stillInGroup) {
            setCurrentGroup(null);
            setMessages([]);
          }
        }
      }

      setLoading(false);
      return response.data;
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  // Delete a chat group
  const deleteChatGroup = async (groupId) => {
    try {
      setLoading(true);
      const response = await axios.delete(
        `${server}/api/chat/group/${groupId}`
      );

      if (response.data.success) {
        await fetchChatGroups();

        if (currentGroup === groupId) {
          setCurrentGroup(null);
          setMessages([]);
        }
      }

      setLoading(false);
      return response.data;
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  return (
    <ChatContext.Provider
      value={{
        chatGroups,
        messages,
        loading,
        currentGroup,
        unreadCounts,
        fetchChatGroups,
        joinChatRoom,
        sendMessage,
        createChatGroup,
        updateChatGroup,
        deleteChatGroup,
        loadMoreMessages,
        hasMore,
        loadingMore,
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};
