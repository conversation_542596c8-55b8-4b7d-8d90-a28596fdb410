import * as yup from "yup";

export const registerSchema = yup.object({
  school_name: yup
    .string()
    .min(8, "School name must contain 8 characters.")
    .required("School name is requried"),
  email: yup
    .string()
    .email("It must be an Email.")
    .required("Email is requried"),
  owner_name: yup
    .string()
    .min(3, "Owner name must have 3 characters.")
    .required("it is requried field"),
  password: yup
    .string()
    .min(8, "Password must contain 8 characters.")
    .required("Password is a requried field"),
  confirm_password: yup
    .string()
    .oneOf([yup.ref("password")], "Confirm Password Must Match With Password.")
    .required("Confirm password is a requried field"),
});
