{"name": "sms-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/lab": "^7.0.0-beta.12", "@mui/material": "^7.0.2", "@mui/x-charts": "^8.3.1", "@mui/x-date-pickers": "^8.3.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "formik": "^2.4.6", "jspdf": "^3.0.1", "moment": "^2.30.1", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-big-calendar": "^1.18.0", "react-dom": "^19.0.0", "react-qr-reader": "^3.0.0-beta-1", "react-qrcode-logo": "^3.0.0", "react-router-dom": "^7.5.2", "react-time-picker": "^7.0.0", "socket.io-client": "^4.8.1", "yup": "^1.6.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "react-beautiful-dnd": "^13.1.1"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.1"}}