import React, { useState } from "react";

import { useFormik } from "formik";
import { useEffect } from "react";
import axios from "axios";
import { server } from "../../../server";
import { periodSchema } from "../../../yupSchema/periodSchema";
import MessageSnackbar from "../../../basic utility component/snakebar/MessageSnakebar";
import { Calendar, momentLocalizer } from "react-big-calendar";
import moment from "moment";
import "react-big-calendar/lib/css/react-big-calendar.css";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
} from "@mui/material";

import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";

const localizer = momentLocalizer(moment);

export default function StudentScheduel() {
  const [newperiod, setNewPeriod] = useState(false);
  const [view, setView] = useState("week"); // Add this line
  const date = new Date();
  const [classes, setClasses] = useState([]);
  const [subjects, setSubjects] = useState([]);
  const [teachers, setTeachers] = useState([]);
  const [message, setMessage] = useState("");
  const [messageType, setMessageType] = useState("success");
  const [params, setParams] = useState({});
  const [events, setEvents] = useState([]);
  const [scheduleData, setScheduleData] = useState([]);
  const handleClass = (e) => {
    const value = e.target.value || undefined;
    setParams((prev) => ({ ...prev, Class: value }));
  };

  const days = [
    "Sunday",
    "Monday",
    "Tuesday",
    "wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];
  const period = [
    "first",
    "Second",
    "Third",
    "Fourth",
    "Fifth",
    "Sixth",
    "Seventh",
    "Eight",
    "Ninth",
    "Tenth",
  ];

  useEffect(() => {
    console.log(params);
    const fetchSchedule = () => {
      axios
        .get(`${server}/api/schedule/fetch-with-query`, { params })
        .then((response) => {
          console.log("schedule_data", response.data.schedules);
          setScheduleData(response.data.schedules);
        })
        .catch((e) => {
          console.log("Error in fetching Students.");
        });
    };

    fetchSchedule();
  }, [message, params]);

  const fetchClasses = () => {
    axios
      .get(`${server}/api/class/all`)
      .then((response) => {
        setClasses(response.data.data);
      })
      .catch((e) => {
        console.log("Error in fetching class.");
      });
  };

  useEffect(() => {
    fetchClasses();
  }, []);

  const getScheduleFor = (day, period) => {
    return scheduleData.filter(
      (item) =>
        item.days === day && item.period.toLowerCase() === period.toLowerCase()
    );
  };

  return (
    <>
      <Typography variant="h3" gutterBottom align="center">
        Weekly Class Schedule
      </Typography>

      <Box
        component={"div"}
        sx={{
          marginTop: "30px",
        }}
      >
        <FormControl sx={{ width: "230px", marginLeft: "10px" }}>
          <InputLabel id="demo-simple-select-label">Student Class</InputLabel>
          <Select
            label="Student Class"
            onChange={(e) => {
              handleClass(e);
            }}
          >
            <MenuItem value=""> Un-Select</MenuItem>
            {classes &&
              classes.map((x) => {
                return (
                  <MenuItem key={x._id} value={x._id}>
                    {x.class_text} ({x.class_num})
                  </MenuItem>
                );
              })}
          </Select>
        </FormControl>
      </Box>

      {params.Class ? (
        <>
          <Box sx={{ p: 3 }}>
            <TableContainer component={Paper} sx={{ overflowX: "auto" }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <strong>Day</strong>
                    </TableCell>
                    {period.map((p) => (
                      <TableCell key={p} align="center">
                        <strong>
                          {p.charAt(0).toUpperCase() + p.slice(1)} Period
                        </strong>
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>

                <TableBody>
                  {days.map((day) => (
                    <TableRow key={day}>
                      <TableCell>{day}</TableCell>
                      {period.map((period) => {
                        const entries = getScheduleFor(day, period);
                        return (
                          <TableCell key={period} align="center">
                            {entries.length > 0 ? (
                              entries.map((entry, idx) => (
                                <Box
                                  key={idx}
                                  mb={1}
                                  p={1}
                                  sx={{
                                    border: "1px solid #eee",
                                    borderRadius: 1,
                                    cursor: "pointer",
                                    transition: "0.2s",
                                    "&:hover": { backgroundColor: "#f5f5f5" },
                                  }}
                                >
                                  <Typography
                                    variant="subtitle2"
                                    sx={{ fontWeight: "bold" }}
                                  >
                                    {entry.subject.subject_codename}
                                  </Typography>
                                  <Typography variant="body2">
                                    {entry.startTime} - {entry.endTime}
                                  </Typography>
                                  <Typography variant="body2">
                                    {entry.class.class_text} -
                                    {entry.class.class_num}
                                  </Typography>
                                  <Typography
                                    variant="body2"
                                    color="textSecondary"
                                  >
                                    {entry.teacher.name}
                                  </Typography>
                                </Box>
                              ))
                            ) : (
                              <Typography variant="body2" color="textSecondary">
                                —
                              </Typography>
                            )}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </>
      ) : (
        <Typography>Select Class to Show Scheduel</Typography>
      )}
    </>
  );
}
