import * as React from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";

import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { server } from "../../../server";
import axios from "axios";
import { Avatar, Box } from "@mui/material";

export default function StudentDetails() {
  const [studentDetails, setStudentDetails] = React.useState(null);
  const fetchStudentDetails = async () => {
    try {
      const response = await axios.get(`${server}/api/student/fetch-single`);
      console.log("student details", response.data.student);
      setStudentDetails(response.data.student);
    } catch (error) {
      console.log("Error in student details fetching single data.", error);
    }
  };

  React.useEffect(() => {
    fetchStudentDetails();
  }, []);

  return (
    <>
      {studentDetails && (
        <>
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              marginBottom: "10px",
              padding: "25px",
            }}
          >
            <Avatar
              alt="Remy Sharp"
              src={`./images/uploaded/student/${studentDetails.student_image}`}
              sx={{ width: 356, height: 356 }}
            />
          </Box>
          <TableContainer component={Paper}>
            <Table sx={{ minWidth: 650 }} aria-label="simple table">
              <TableBody>
                <TableRow>
                  <TableCell>
                    <b>Name:</b>
                  </TableCell>
                  <TableCell align="right">{studentDetails.name}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>
                    <b>Email:</b>
                  </TableCell>
                  <TableCell align="right">{studentDetails.email}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>
                    <b>Age:</b>
                  </TableCell>
                  <TableCell align="right">{studentDetails.age}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>
                    <b>Gender:</b>
                  </TableCell>
                  <TableCell align="right">{studentDetails.gender}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>
                    <b> Class:</b>
                  </TableCell>
                  <TableCell align="right">
                    {studentDetails.student_class.class_text}[
                    {studentDetails.student_class.class_num}]
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>
                    <b>Guardian:</b>
                  </TableCell>
                  <TableCell align="right">{studentDetails.guardian}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>
                    <b>Guardian Phone:</b>
                  </TableCell>
                  <TableCell align="right">
                    {studentDetails.guardian_phone}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </>
      )}
    </>
  );
}
