import React, { useContext, useState, useEffect, useRef } from "react";
import { ChatContext } from "../../../context/ChatContext";
import { AuthContext } from "../../../context/AuthContext";
import {
  Box,
  Typography,
  Paper,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  TextField,
  Button,
  Card,
  CardContent,
  Badge,
  CircularProgress,
} from "@mui/material";
import SendIcon from "@mui/icons-material/Send";
import GroupIcon from "@mui/icons-material/Group";

const TeacherChat = () => {
  const { user } = useContext(AuthContext);
  const {
    chatGroups,
    messages,
    currentGroup,
    unreadCounts,
    fetchChatGroups,
    joinChatRoom,
    sendMessage,
    loadMoreMessages,
    loadingMore,
    hasMore,
  } = useContext(ChatContext);

  const [newMessage, setNewMessage] = useState("");
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);
  const [prevMessagesLength, setPrevMessagesLength] = useState(0);
  const [localLoadingMore, setLocalLoadingMore] = useState(false);

  // Fetch chat groups on component mount
  useEffect(() => {
    fetchChatGroups();
  }, []);

  // Handle scrolling to bottom when new messages are added
  useEffect(() => {
    // Only auto-scroll if we're already at the bottom or if we sent a new message
    const container = messagesContainerRef.current;
    if (container) {
      const isAtBottom =
        container.scrollHeight - container.scrollTop <=
        container.clientHeight + 100;

      // If messages were added at the end (new message sent/received)
      if (messages.length > prevMessagesLength && isAtBottom) {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
      }
    }

    setPrevMessagesLength(messages.length);
  }, [messages]);

  // Handle infinite scrolling
  const handleScroll = () => {
    const container = messagesContainerRef.current;
    if (
      container &&
      container.scrollTop === 0 &&
      hasMore &&
      !localLoadingMore
    ) {
      // Set local loading state
      setLocalLoadingMore(true);

      // Save current scroll height
      const scrollHeight = container.scrollHeight;

      // Load more messages
      loadMoreMessages();

      // After loading, restore scroll position
      setTimeout(() => {
        if (container) {
          const newScrollHeight = container.scrollHeight;
          container.scrollTop = newScrollHeight - scrollHeight;
          setLocalLoadingMore(false);
        }
      }, 300); // Increased timeout to ensure messages are loaded
    }
  };

  const handleSendMessage = (e) => {
    e.preventDefault();
    if (newMessage.trim()) {
      sendMessage(newMessage);
      setNewMessage("");

      // Focus back on the input field after sending
      setTimeout(() => {
        const inputField = document.getElementById(
          "teacher-chat-message-input"
        );
        if (inputField) inputField.focus();
      }, 0);
    }
  };

  // Find current group details
  const currentGroupDetails = chatGroups.find((g) => g._id === currentGroup);

  return (
    <Box
      sx={{
        height: "calc(100vh - 100px)",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Typography variant="h5" sx={{ mb: 2 }}>
        Teacher Chat
      </Typography>

      <Grid container spacing={2} sx={{ flexGrow: 1 }}>
        {/* Chat Groups List */}
        <Grid item xs={3}>
          <Paper sx={{ height: "100%", overflow: "auto" }}>
            <Box sx={{ p: 1 }}>
              <Typography variant="h6">Groups</Typography>
            </Box>
            <Divider />
            <List>
              {chatGroups.map((group) => (
                <ListItem
                  key={group._id}
                  button
                  selected={currentGroup === group._id}
                  onClick={() => joinChatRoom(group._id)}
                >
                  <ListItemAvatar>
                    <Badge
                      badgeContent={unreadCounts[group._id] || 0}
                      color="error"
                      invisible={
                        !unreadCounts[group._id] || unreadCounts[group._id] <= 0
                      }
                      sx={{
                        ".MuiBadge-badge": {
                          fontSize: "0.8rem",
                          height: "20px",
                          minWidth: "20px",
                        },
                      }}
                    >
                      <Avatar>
                        <GroupIcon />
                      </Avatar>
                    </Badge>
                  </ListItemAvatar>
                  <ListItemText
                    primary={group.name}
                    secondary={`${group.members.length} members`}
                  />
                </ListItem>
              ))}
              {chatGroups.length === 0 && (
                <ListItem>
                  <ListItemText primary="No chat groups yet" />
                </ListItem>
              )}
            </List>
          </Paper>
        </Grid>

        {/* Chat Messages */}
        <Grid item xs={9}>
          <Paper
            sx={{
              height: "100%",
              display: "flex",
              flexDirection: "column",
              maxHeight: "calc(100vh - 120px)", // Ensure the paper has a max height
            }}
          >
            {currentGroup ? (
              <>
                <Box sx={{ p: 2, borderBottom: 1, borderColor: "divider" }}>
                  <Typography variant="h6">
                    {currentGroupDetails?.name || "Loading..."}
                  </Typography>
                </Box>

                {/* Messages container with fixed height */}
                <Box
                  ref={messagesContainerRef}
                  sx={{
                    flexGrow: 1,
                    overflow: "auto",
                    p: 2,
                    height: "calc(100% - 120px)", // Ensure the box has a fixed height
                    display: "flex",
                    flexDirection: "column",
                  }}
                  onScroll={handleScroll}
                >
                  {(localLoadingMore || loadingMore) && (
                    <Box
                      sx={{ display: "flex", justifyContent: "center", mb: 2 }}
                    >
                      <CircularProgress size={24} />
                    </Box>
                  )}

                  {messages.map((message, index) => (
                    <Box
                      key={message._id || message.tempId || index}
                      sx={{
                        display: "flex",
                        justifyContent:
                          message.senderId === user.id
                            ? "flex-end"
                            : "flex-start",
                        mb: 2,
                      }}
                    >
                      <Card
                        sx={{
                          maxWidth: "70%",
                          bgcolor:
                            message.senderId === user.id
                              ? "primary.light"
                              : "grey.100",
                        }}
                      >
                        <CardContent
                          sx={{ py: 1, px: 2, "&:last-child": { pb: 1 } }}
                        >
                          <Typography variant="caption" color="text.secondary">
                            {message.senderInfo?.name || "Unknown"}
                          </Typography>
                          <Typography variant="body1">
                            {message.content}
                          </Typography>
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{ display: "block", textAlign: "right" }}
                          >
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Box>
                  ))}

                  {messages.length === 0 && (
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        height: "100%",
                      }}
                    >
                      <Typography color="text.secondary">
                        No messages yet
                      </Typography>
                    </Box>
                  )}

                  {/* Invisible element to scroll to */}
                  <div ref={messagesEndRef} />
                </Box>

                {/* Message Input */}
                <Box sx={{ p: 2, borderTop: 1, borderColor: "divider" }}>
                  <form onSubmit={handleSendMessage}>
                    <Box sx={{ display: "flex" }}>
                      <TextField
                        id="teacher-chat-message-input"
                        fullWidth
                        placeholder="Type a message"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        variant="outlined"
                        size="small"
                      />
                      <Button
                        type="submit"
                        variant="contained"
                        sx={{ ml: 1 }}
                        disabled={!newMessage.trim()}
                      >
                        <SendIcon />
                      </Button>
                    </Box>
                  </form>
                </Box>
              </>
            ) : (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  height: "100%",
                }}
              >
                <Typography color="text.secondary">
                  Select a chat group to start messaging
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default TeacherChat;
