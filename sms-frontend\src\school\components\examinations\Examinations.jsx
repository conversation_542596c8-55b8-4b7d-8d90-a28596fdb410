import * as React from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import TimePicker from "react-time-picker";
import "react-time-picker/dist/TimePicker.css";
import "react-clock/dist/Clock.css";

import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  selectClasses,
  TextField,
  Typography,
} from "@mui/material";
import { useFormik } from "formik";
import { examinationSchema } from "../../../yupSchema/examinationSchema";
import dayjs from "dayjs";
import axios from "axios";
import { server } from "../../../server";
import MessageSnackbar from "../../../basic utility component/snakebar/MessageSnakebar";
// react calender
import { Calendar, momentLocalizer } from "react-big-calendar";
import moment from "moment";
import "react-big-calendar/lib/css/react-big-calendar.css";

import {
  DndContext,
  useSensor,
  useSensors,
  PointerSensor,
  DragOverlay,
  useDroppable,
} from "@dnd-kit/core";
import { restrictToWindowEdges } from "@dnd-kit/modifiers";
import { CSS } from "@dnd-kit/utilities";
import { useSortable } from "@dnd-kit/sortable";

// Generic Item component for drag and drop
const DraggableItem = ({ id, text, subtitle, isActive }) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    padding: "8px 12px",
    margin: "4px",
    border: "1px solid #ccc",
    borderRadius: "4px",
    backgroundColor: isActive ? "#e0e0e0" : "#f5f5f5",
    cursor: "grab",
    userSelect: "none",
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <div>{text}</div>
      {subtitle && (
        <div style={{ fontSize: "0.8rem", color: "#666" }}>{subtitle}</div>
      )}
    </div>
  );
};

// Generic drop zone component
const DropZone = ({ id, selectedItem, itemText, placeholder }) => {
  const { setNodeRef, isOver } = useDroppable({
    id,
  });

  return (
    <div
      ref={setNodeRef}
      style={{
        border: `2px dashed ${isOver ? "#4caf50" : "#1976d2"}`,
        borderRadius: "8px",
        padding: "16px",
        minHeight: "60px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: isOver
          ? "rgba(76, 175, 80, 0.1)"
          : selectedItem
          ? "#e3f2fd"
          : "#f5f5f5",
        marginBottom: "16px",
        transition: "all 0.2s ease",
      }}
    >
      {selectedItem ? (
        <div
          style={{
            padding: "8px 12px",
            borderRadius: "4px",
            backgroundColor: "#1976d2",
            color: "white",
          }}
        >
          {itemText}
        </div>
      ) : (
        <Typography color="textSecondary">
          {isOver ? "Drop to select" : placeholder}
        </Typography>
      )}
    </div>
  );
};

// Generic selector with drag and drop
const DragDropSelector = ({
  title,
  items,
  value,
  onChange,
  fieldName,
  getItemText,
  getItemSubtitle,
  dropzoneId,
  sx = {},
}) => {
  const [activeId, setActiveId] = React.useState(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    })
  );

  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = (event) => {
    const { over, active } = event;
    setActiveId(null);

    if (over && over.id === dropzoneId) {
      // Item was dropped in the dropzone
      onChange({
        target: {
          name: fieldName,
          value: active.id,
        },
      });
    } else if (!over && value === active.id) {
      // Item was dragged out and it was the selected one
      onChange({
        target: {
          name: fieldName,
          value: "",
        },
      });
    }
  };

  const handleDragCancel = () => {
    setActiveId(null);
  };

  // Filter out the selected item from available options
  const availableItems = items
    ? items.filter((item) =>
        typeof item === "object" ? item._id !== value : item !== value
      )
    : [];

  const selectedItem = items
    ? items.find((item) =>
        typeof item === "object" ? item._id === value : item === value
      )
    : null;

  const selectedItemText = selectedItem ? getItemText(selectedItem) : "";

  return (
    <div style={{ marginTop: "20px", ...sx }}>
      <Typography variant="subtitle1" sx={{ mb: 1 }}>
        {title}
      </Typography>
      <DndContext
        sensors={sensors}
        modifiers={[restrictToWindowEdges]}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragCancel={handleDragCancel}
      >
        <DropZone
          id={dropzoneId}
          selectedItem={value}
          itemText={selectedItemText}
          placeholder={`Drag a ${title.toLowerCase()} here`}
        />

        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          Options{" "}
          {availableItems.length < (items?.length || 0)
            ? `(${availableItems.length} of ${items?.length})`
            : ""}
        </Typography>
        <div style={{ display: "flex", flexWrap: "wrap", maxWidth: "100%" }}>
          {availableItems.map((item) => (
            <DraggableItem
              key={typeof item === "object" ? item._id : item}
              id={typeof item === "object" ? item._id : item}
              text={getItemText(item)}
              subtitle={getItemSubtitle ? getItemSubtitle(item) : null}
              isActive={
                activeId === (typeof item === "object" ? item._id : item)
              }
            />
          ))}
        </div>

        <DragOverlay>
          {activeId
            ? (() => {
                const activeItem = items?.find((item) =>
                  typeof item === "object"
                    ? item._id === activeId
                    : item === activeId
                );
                if (activeItem) {
                  return (
                    <div
                      style={{
                        padding: "8px 12px",
                        borderRadius: "4px",
                        backgroundColor: "#bbdefb",
                        boxShadow: "0 5px 10px rgba(0,0,0,0.2)",
                      }}
                    >
                      {getItemText(activeItem)}
                      {getItemSubtitle && (
                        <div style={{ fontSize: "0.8rem", color: "#666" }}>
                          {getItemSubtitle(activeItem)}
                        </div>
                      )}
                    </div>
                  );
                }
                return null;
              })()
            : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
};

export default function Examinations() {
  const [examinations, setExaminations] = React.useState([]);
  const [classes, setClasses] = React.useState([]);
  const [subjects, setSubjects] = React.useState([]);
  const [teachers, setTeachers] = React.useState([]);
  const [calendarEvents, setCalendarEvents] = React.useState([]);
  const [selectedClass, setSelectedClass] = React.useState("");
  const [message, setMessage] = React.useState("");
  const [messageType, setMessageType] = React.useState("success");
  const [editId, setEditId] = React.useState(null);
  const [addExam, setAddExam] = React.useState(false);
  const [show, setShow] = React.useState(false);
  const [calendarView, setCalendarView] = React.useState("week");
  const [calendarDate, setCalendarDate] = React.useState(new Date());
  const handleMessageClose = () => {
    setMessage("");
  };
  const handleMessageNew = (msg, type) => {
    setMessage(msg);
    setMessageType(type);
  };
  const dateformat = (dateData) => {
    const date = new Date(dateData);

    return (
      date.getDate() + "-" + (+date.getMonth() + 1) + "-" + date.getFullYear()
    );
  };
  const formatTimeTo12Hour = (time24) => {
    const [hours, minutes] = time24.split(":");
    const date = new Date();
    date.setHours(+hours);
    date.setMinutes(+minutes);
    return date.toLocaleTimeString([], {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  };
  const handleEdit = (id) => {
    setEditId(id);
    setAddExam(true);
    const selectedExamination = examinations.filter((x) => x._id === id);
    Formik.setFieldValue("date", selectedExamination[0].examDate);
    Formik.setFieldValue("subject", selectedExamination[0].subject._id);
    Formik.setFieldValue("Class", selectedExamination[0].class._id);
    Formik.setFieldValue("teacher", selectedExamination[0].teacher._id);
    Formik.setFieldValue("invigilator", selectedExamination[0].invigilator._id);
    Formik.setFieldValue("examType", selectedExamination[0].examType);
    Formik.setFieldValue("startTime", selectedExamination[0].startTime);
    Formik.setFieldValue("endTime", selectedExamination[0].endTime);
  };
  const handleEditCancel = () => {
    setEditId(null);
    setAddExam(false);
    Formik.resetForm();
  };
  const handleDelete = async (id) => {
    if (confirm("Are you sure you want to delete?")) {
      try {
        const response = await axios.delete(
          `${server}/api/examination/delete/${id}`
        );
        console.log("DELETE", response);
        setMessage(response.data.message);
        setMessageType("success");
      } catch (error) {
        setMessage("Error in Deleting Examination.");
        setMessageType("error");
        console.log(
          "ERROR: (Deleting Examination - Examination Component)",
          error
        );
      }
    }
  };
  const initialValues = {
    date: "",
    subject: "",
    Class: "",
    teacher: "",
    invigilator: "",
    examType: "",
    startTime: "",
    endTime: "",
  };

  const Formik = useFormik({
    initialValues: initialValues,
    validationSchema: examinationSchema, // Fixed casing
    onSubmit: async (values) => {
      console.log("Examination", values);
      // Add your form submission logic here
      try {
        if (editId) {
          const response = await axios.patch(
            `${server}/api/examination/update/${editId}`,
            {
              ...values,
            }
          );
          console.log("RESPONSE Upadte EXAMINATION", response.data);
          setMessage(response.data.message);
          setMessageType("success");
          setEditId(null);
          Formik.resetForm();
        } else {
          const response = await axios.post(
            `${server}/api/examination/create`,
            {
              ...values,
            }
          );
          console.log("RESPONSE NEW EXAMINATION", response.data);
          setMessage(response.data.message);
          setMessageType("success");
          Formik.resetForm();
        }
      } catch (error) {
        setMessage("Error in saving exam");
        setMessageType("error");
        console.error(
          "Error saving new examination - Examination Component:",
          error
        );
        // Consider adding error state handling here
      }
    },
  });
  const fetchClasses = () => {
    axios
      .get(`${server}/api/class/all`)
      .then((response) => {
        setClasses(response.data.data);
      })
      .catch((e) => {
        console.log("Error in fetching class.");
      });
  };
  const fetchAllSubjects = () => {
    axios
      .get(`${server}/api/subject/all`)
      .then((response) => {
        setSubjects(response.data.data);
      })
      .catch((e) => {
        console.log("Error in fetching all subjects", e);
      });
  };
  const fetchTeachers = () => {
    axios
      .get(`${server}/api/teacher/fetch-with-query`)
      .then((response) => {
        setTeachers(response.data.teachers);
      })
      .catch((e) => {
        console.log("Error in fetching Teachers.");
      });
  };
  const fetchExaminations = async () => {
    try {
      if (selectedClass) {
        const response = await axios.get(
          `${server}/api/examination/class/${selectedClass}`
        );
        console.log(response.data.examinations);
        setExaminations(response.data.examinations);
      } else {
        const response = await axios.get(`${server}/api/examination/all`);
        console.log(response.data.examinations);
        setExaminations(response.data.examinations);
      }
    } catch (error) {
      console.log(
        "ERROR--> (Saving Fetching Examination- Examination Component",
        error
      );
    }
  };
  React.useEffect(() => {
    fetchExaminations();
  }, [message, selectedClass]);

  React.useEffect(() => {
    fetchClasses();
    fetchTeachers();
    fetchAllSubjects();
  }, []);

  const localizer = momentLocalizer(moment);

  const now = new Date();
  const dummyEvents = [
    {
      id: 1,
      title: "Math Exam",
      start: new Date(2025, 4, 23, 10, 0), // May is month 4 (0-indexed)
      end: new Date(2025, 4, 23, 12, 0),
    },
    {
      id: 2,
      title: "Physics Exam",
      start: new Date(2025, 4, 24, 9, 30),
      end: new Date(2025, 4, 24, 11, 30),
    },
    {
      id: 3,
      title: "Chemistry Exam",
      start: new Date(2025, 4, 25, 13, 0),
      end: new Date(2025, 4, 25, 15, 0),
    },
  ];

  const formatExamsForCalendar = (examData) => {
    return examData.map((item, index) => {
      const examDate = new Date(item.examDate); // e.g., "2025-05-12T19:00:00.000Z"

      const [startHour, startMinute] = item.startTime.split(":");
      const [endHour, endMinute] = item.endTime.split(":");

      const start = new Date(examDate);
      start.setHours(+startHour, +startMinute, 0, 0);

      const end = new Date(examDate);
      end.setHours(+endHour, +endMinute, 0, 0);

      return {
        id: item._id,
        title: `Subject:${item.subject?.subject_name || "title"} Teacher:${
          item.teacher?.name || "teacher"
        } Invigilator:${item.invigilator?.name} Class:${
          item.class?.class_text
        }/${item.class?.class_num}`,

        start,
        end,
      };
    });
  };

  React.useEffect(() => {
    const calendardata = formatExamsForCalendar(examinations);

    setCalendarEvents(calendardata);
    console.log("calender event", calendardata);
  }, [examinations]);

  return (
    <>
      {message && (
        <MessageSnackbar
          message={message}
          type={messageType}
          handleClose={handleMessageClose}
        />
      )}
      <Paper
        sx={{
          display: "flex",

          marginBottom: "10px",
          justifyContent: "space-between",
        }}
      >
        <Box>
          {/* class */}
          <FormControl sx={{ width: "20vw" }}>
            <InputLabel id="demo-simple-select-label">Class</InputLabel>
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              label="Class"
              name="class"
              onChange={(e) => setSelectedClass(e.target.value)}
              value={selectedClass}
            >
              <MenuItem value={""}>Un Select</MenuItem>
              {classes &&
                classes.map((x) => {
                  return (
                    <MenuItem key={x._id} value={x._id}>
                      {x.class_text} ({x.class_num})
                    </MenuItem>
                  );
                })}
            </Select>
          </FormControl>
        </Box>
        {selectedClass === "" ? (
          <></>
        ) : (
          <>
            <Box sx={{ display: "flex", paddingTop: "10px" }}>
              <Box>
                {addExam ? (
                  <Button
                    variant="contained"
                    onClick={(() => setAddExam(false), handleEditCancel)}
                  >
                    Cancle
                  </Button>
                ) : (
                  <Button variant="contained" onClick={() => setAddExam(true)}>
                    Add New Exam
                  </Button>
                )}
              </Box>
              <Box>
                {show ? (
                  <Button variant="contained" onClick={() => setShow(false)}>
                    Show Calender
                  </Button>
                ) : (
                  <Button variant="contained" onClick={() => setShow(true)}>
                    Show List
                  </Button>
                )}
              </Box>
            </Box>
          </>
        )}
      </Paper>

      {addExam && (
        <Paper>
          <Box
            component="form"
            noValidate
            autoComplete="off"
            onSubmit={Formik.handleSubmit}
            sx={{ width: "24vw", minWidth: "310px", margin: "auto" }}
          >
            {editId ? (
              <Typography variant="h4">Edit Exam</Typography>
            ) : (
              <Typography variant="h4">Add New Exam</Typography>
            )}
            <LocalizationProvider dateAdapter={AdapterDayjs} fullWidth>
              <DemoContainer components={["DatePicker"]} fullWidth>
                <DatePicker
                  fullWidth
                  label="Basic date picker"
                  value={Formik.values.date ? dayjs(Formik.values.date) : null}
                  onChange={(newValue) => {
                    Formik.setFieldValue("date", newValue);
                  }}
                  slotProps={{
                    textField: {
                      error: Formik.touched.date && Boolean(Formik.errors.date),
                      helperText: Formik.touched.date && Formik.errors.date,
                    },
                  }}
                />
              </DemoContainer>
            </LocalizationProvider>
            {Formik.touched.date && Formik.errors.date && (
              <p style={{ color: "red", textTransform: "capitalize" }}>
                {Formik.errors.date}
              </p>
            )}
            {/* start time */}
            <Box sx={{ marginTop: "20px" }} fullWidth>
              <InputLabel id="demo-simple-select-label">Start Time</InputLabel>
              <TimePicker
                label="Start Time"
                value={Formik.values.startTime}
                onChange={(value) => Formik.setFieldValue("startTime", value)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error:
                      Formik.touched.startTime &&
                      Boolean(Formik.errors.startTime),
                    helperText:
                      Formik.touched.startTime && Formik.errors.startTime,
                  },
                }}
              />
            </Box>
            {Formik.touched.startTime && Formik.errors.startTime && (
              <p style={{ color: "red", textTransform: "capitalize" }}>
                {Formik.errors.startTime}
              </p>
            )}
            {/* End time */}
            <Box sx={{ marginTop: "20px" }} fullWidth>
              <InputLabel id="demo-simple-select-label">End Time</InputLabel>
              <TimePicker
                label="End Time"
                value={Formik.values.endTime}
                onChange={(value) => Formik.setFieldValue("endTime", value)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error:
                      Formik.touched.endTime && Boolean(Formik.errors.endTime),
                    helperText: Formik.touched.endTime && Formik.errorsendtTime,
                  },
                }}
              />
            </Box>
            {Formik.touched.endTime && Formik.errors.endTime && (
              <p style={{ color: "red", textTransform: "capitalize" }}>
                {Formik.errors.endTime}
              </p>
            )}

            {/* class */}
            <DragDropSelector
              title="Class"
              items={classes}
              value={Formik.values.Class}
              onChange={Formik.handleChange}
              fieldName="Class"
              getItemText={(item) => `${item.class_text} (${item.class_num})`}
              dropzoneId="class-dropzone"
              sx={{ marginTop: "20px" }}
            />
            {Formik.touched.Class && Formik.errors.Class && (
              <p style={{ color: "red", textTransform: "capitalize" }}>
                {Formik.errors.Class}
              </p>
            )}
            {/* teacher */}
            <DragDropSelector
              title="Teacher"
              items={teachers}
              value={Formik.values.teacher}
              onChange={Formik.handleChange}
              fieldName="teacher"
              getItemText={(item) => `${item.name}`}
              getItemSubtitle={(item) => `${item.qualification}`}
              dropzoneId="teacher-dropzone"
              sx={{ marginTop: "20px" }}
            />
            {Formik.touched.teacher && Formik.errors.teacher && (
              <p style={{ color: "red", textTransform: "capitalize" }}>
                {Formik.errors.teacher}
              </p>
            )}
            {/* invigilator */}
            <DragDropSelector
              title="Invigilator"
              items={teachers}
              value={Formik.values.invigilator}
              onChange={Formik.handleChange}
              fieldName="invigilator"
              getItemText={(item) => `${item.name}`}
              dropzoneId="invigilator-dropzone"
              sx={{ marginTop: "20px" }}
            />
            {Formik.touched.invigilator && Formik.errors.invigilator && (
              <p style={{ color: "red", textTransform: "capitalize" }}>
                {Formik.errors.invigilator}
              </p>
            )}
            {/* subject */}
            <DragDropSelector
              title="Subject"
              items={subjects}
              value={Formik.values.subject}
              onChange={Formik.handleChange}
              fieldName="subject"
              getItemText={(item) => `${item.subject_name}`}
              getItemSubtitle={(item) => `${item.subject_codename}`}
              dropzoneId="subject-dropzone"
              sx={{ marginTop: "20px" }}
            />
            {Formik.touched.subject && Formik.errors.subject && (
              <p style={{ color: "red", textTransform: "capitalize" }}>
                {Formik.errors.subject}
              </p>
            )}
            <TextField
              fullWidth
              name="examType"
              onChange={Formik.handleChange}
              onBlur={Formik.handleChange}
              value={Formik.values.examType}
              label="Exam Type"
              variant="standard"
              sx={{ marginTop: "20px" }}
            />
            {Formik.touched.examType && Formik.errors.examType && (
              <p style={{ color: "red", textTransform: "capitalize" }}>
                {Formik.errors.examType}
              </p>
            )}

            <Button type="submit" variant="contained">
              Submit
            </Button>
            {editId && (
              <Button
                type="submit"
                variant="contained"
                onClick={handleEditCancel}
              >
                Cancel
              </Button>
            )}
          </Box>
        </Paper>
      )}

      {selectedClass === "" ? (
        <Typography>Select Class to Show</Typography>
      ) : (
        <>
          {show ? (
            <TableContainer component={Paper}>
              <Table sx={{ minWidth: 650 }} aria-label="simple table">
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <strong>Exam Date</strong>
                    </TableCell>
                    <TableCell>
                      <strong>Exam Time</strong>
                    </TableCell>
                    <TableCell>
                      <strong>Subject</strong>
                    </TableCell>
                    <TableCell>
                      <strong>Class</strong>
                    </TableCell>
                    <TableCell>
                      <strong> Teacher</strong>
                    </TableCell>
                    <TableCell>
                      <strong>Invigilator</strong>
                    </TableCell>
                    <TableCell>
                      <strong>Exam Type</strong>
                    </TableCell>
                    <TableCell>
                      <strong>Action</strong>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {examinations.map((examination) => (
                    <TableRow
                      key={examination._id}
                      sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
                    >
                      <TableCell>{dateformat(examination.examDate)}</TableCell>
                      <TableCell>
                        {formatTimeTo12Hour(examination.startTime)}-
                        {formatTimeTo12Hour(examination.endTime)}
                      </TableCell>
                      <TableCell>{examination.subject.subject_name}</TableCell>
                      <TableCell>{examination.class.class_text}</TableCell>
                      <TableCell>{examination.teacher?.name}</TableCell>
                      <TableCell>{examination.invigilator.name}</TableCell>
                      <TableCell>{examination.examType}</TableCell>
                      <TableCell>
                        <Button
                          variant="contained"
                          sx={{ background: "skyblue" }}
                          onClick={() => handleEdit(examination._id)}
                        >
                          Edit
                        </Button>
                        <Button
                          variant="contained"
                          sx={{ background: "tomato" }}
                          onClick={() => handleDelete(examination._id)}
                        >
                          Delete
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <div style={{ height: "100vh" }}>
              <Calendar
                localizer={localizer}
                events={calendarEvents}
                startAccessor="start"
                endAccessor="end"
                style={{ height: "100%", margin: "20px" }}
                defaultView={calendarView}
                view={calendarView}
                date={calendarDate}
                min={new Date(1970, 1, 1, 8, 0, 0)}
                max={new Date(1970, 1, 1, 22, 0, 0)}
                onNavigate={(date) => setCalendarDate(date)}
                onView={(view) => setCalendarView(view)}
                sx={{ height: "100%", width: "100%" }}
              />
            </div>
          )}
        </>
      )}
    </>
  );
}
